// 管理员专用功能模块
class AdminManager {
    constructor() {
        this.currentUserList = [];
        this.currentPage = 1;
        this.pageSize = 10;
    }

    // 初始化管理员功能
    init() {
        if (!appState.isAdmin) {
            console.warn('当前用户不是管理员，无法使用管理员功能');
            return;
        }

        this.bindAdminEvents();
        console.log('管理员功能已初始化');
    }

    // 绑定管理员相关事件
    bindAdminEvents() {
        // 用户管理相关事件将在这里绑定
        this.initUserManagement();
    }

    // 初始化用户管理功能
    initUserManagement() {
        // 当切换到用户管理标签时加载数据
        const usersTab = document.querySelector('[data-tab="users"]');
        if (usersTab) {
            usersTab.addEventListener('click', () => {
                setTimeout(() => this.loadUserList(), 100);
            });
        }
    }

    // 加载用户列表
    async loadUserList(page = 1) {
        if (!appState.isAdmin) {
            toast.error('权限不足');
            return;
        }

        try {
            // 注意：这个API在当前文档中没有定义，这里是模拟数据
            // 实际使用时需要后端提供相应的接口
            const mockUsers = this.generateMockUserData();
            this.currentUserList = mockUsers;
            this.renderUserTable(mockUsers);
            
            toast.info('用户数据已加载（模拟数据）');
        } catch (error) {
            console.error('加载用户列表失败:', error);
            toast.error('加载用户列表失败');
        }
    }

    // 生成模拟用户数据
    generateMockUserData() {
        const mockUsers = [];
        const phones = ['13800138001', '13800138002', '13800138003', '13800138004', '13800138005'];
        
        for (let i = 1; i <= 5; i++) {
            mockUsers.push({
                id: i,
                phone: phones[i - 1],
                balance: Math.random() * 1000,
                status: Math.random() > 0.8 ? 2 : 1, // 20%概率为冻结状态
                created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
                updated_at: new Date().toISOString()
            });
        }
        
        return mockUsers;
    }

    // 渲染用户表格
    renderUserTable(users) {
        const tableBody = document.querySelector('#usersTable tbody');
        if (!tableBody) return;

        tableBody.innerHTML = '';

        if (users.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                        暂无用户数据
                    </td>
                </tr>
            `;
            return;
        }

        users.forEach(user => {
            const row = this.createUserRow(user);
            tableBody.appendChild(row);
        });
    }

    // 创建用户行
    createUserRow(user) {
        const row = document.createElement('tr');
        const statusInfo = ApiUtils.formatUserStatus(user.status);
        
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.phone}</td>
            <td>¥${ApiUtils.formatBalance(user.balance)}</td>
            <td>
                <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
            </td>
            <td>${ApiUtils.formatDateTime(user.created_at)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-secondary" onclick="adminManager.viewUser(${user.id})">
                        查看
                    </button>
                    <button class="btn btn-sm ${user.status === 1 ? 'btn-warning' : 'btn-success'}" 
                            onclick="adminManager.toggleUserStatus(${user.id}, ${user.status})">
                        ${user.status === 1 ? '冻结' : '解冻'}
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="adminManager.deleteUser(${user.id})">
                        删除
                    </button>
                </div>
            </td>
        `;

        return row;
    }

    // 查看用户详情
    async viewUser(userId) {
        const user = this.currentUserList.find(u => u.id === userId);
        if (!user) {
            toast.error('用户不存在');
            return;
        }

        const modalBody = document.getElementById('modalBody');
        if (!modalBody) return;

        modalBody.innerHTML = `
            <h3>用户详情</h3>
            <div class="user-detail">
                <div class="detail-item">
                    <label>用户ID:</label>
                    <span>${user.id}</span>
                </div>
                <div class="detail-item">
                    <label>手机号:</label>
                    <span>${user.phone}</span>
                </div>
                <div class="detail-item">
                    <label>账户余额:</label>
                    <span>¥${ApiUtils.formatBalance(user.balance)}</span>
                </div>
                <div class="detail-item">
                    <label>账户状态:</label>
                    <span class="status-badge ${ApiUtils.formatUserStatus(user.status).class}">
                        ${ApiUtils.formatUserStatus(user.status).text}
                    </span>
                </div>
                <div class="detail-item">
                    <label>注册时间:</label>
                    <span>${ApiUtils.formatDateTime(user.created_at)}</span>
                </div>
                <div class="detail-item">
                    <label>更新时间:</label>
                    <span>${ApiUtils.formatDateTime(user.updated_at)}</span>
                </div>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideModal()">关闭</button>
            </div>
        `;

        showModal();
    }

    // 切换用户状态（冻结/解冻）
    async toggleUserStatus(userId, currentStatus) {
        const user = this.currentUserList.find(u => u.id === userId);
        if (!user) {
            toast.error('用户不存在');
            return;
        }

        const newStatus = currentStatus === 1 ? 2 : 1;
        const action = newStatus === 2 ? '冻结' : '解冻';

        if (!confirm(`确定要${action}用户 ${user.phone} 吗？`)) {
            return;
        }

        try {
            // 注意：这个API在当前文档中没有定义，这里是模拟操作
            // const result = await apiService.toggleUserStatus(userId, newStatus);
            
            // 模拟API调用成功
            user.status = newStatus;
            user.updated_at = new Date().toISOString();
            
            this.renderUserTable(this.currentUserList);
            toast.success(`用户${action}成功`);
            
        } catch (error) {
            console.error(`${action}用户失败:`, error);
            toast.error(`${action}用户失败`);
        }
    }

    // 删除用户
    async deleteUser(userId) {
        const user = this.currentUserList.find(u => u.id === userId);
        if (!user) {
            toast.error('用户不存在');
            return;
        }

        if (!confirm(`确定要删除用户 ${user.phone} 吗？此操作不可恢复！`)) {
            return;
        }

        try {
            // 注意：这个API在当前文档中没有定义，这里是模拟操作
            // const result = await apiService.deleteUser(userId);
            
            // 模拟API调用成功
            this.currentUserList = this.currentUserList.filter(u => u.id !== userId);
            this.renderUserTable(this.currentUserList);
            toast.success('用户删除成功');
            
        } catch (error) {
            console.error('删除用户失败:', error);
            toast.error('删除用户失败');
        }
    }

    // 添加用户
    showAddUserModal() {
        const modalBody = document.getElementById('modalBody');
        if (!modalBody) return;

        modalBody.innerHTML = `
            <h3>添加用户</h3>
            <form id="addUserForm">
                <div class="form-group">
                    <label for="addUserPhone">手机号</label>
                    <input type="tel" id="addUserPhone" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <div class="form-group">
                    <label for="addUserPassword">初始密码</label>
                    <input type="password" id="addUserPassword" name="password" placeholder="6-20位字符" required>
                </div>
                <div class="form-group">
                    <label for="addUserBalance">初始余额</label>
                    <input type="number" id="addUserBalance" name="balance" placeholder="0.00" min="0" step="0.01">
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="hideModal()">取消</button>
                    <button type="submit" class="btn btn-primary">添加用户</button>
                </div>
            </form>
        `;

        // 绑定表单提交事件
        const form = document.getElementById('addUserForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleAddUser(e));
        }

        showModal();
    }

    // 处理添加用户
    async handleAddUser(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const userData = {
            phone: formData.get('phone'),
            password: formData.get('password'),
            balance: parseFloat(formData.get('balance')) || 0
        };

        // 表单验证
        if (!ApiUtils.validatePhone(userData.phone)) {
            toast.error('请输入正确的手机号');
            return;
        }

        if (!ApiUtils.validatePassword(userData.password)) {
            toast.error('密码长度应为6-20位字符');
            return;
        }

        try {
            // 注意：这里应该调用管理员添加用户的API，当前文档中没有定义
            // 这里使用注册API作为替代，实际应用中需要专门的管理员API
            const result = await apiService.register({
                ...userData,
                code: '123456', // 管理员添加用户可能不需要验证码
                invite_code: 'SOLVE2024'
            });

            if (result.success) {
                toast.success('用户添加成功');
                hideModal();
                this.loadUserList(); // 重新加载用户列表
            } else {
                toast.error(ApiUtils.handleApiError(result, '添加用户失败'));
            }
        } catch (error) {
            console.error('添加用户错误:', error);
            toast.error('添加用户失败，请稍后重试');
        }
    }

    // 导出用户数据
    exportUserData() {
        if (this.currentUserList.length === 0) {
            toast.warning('没有可导出的数据');
            return;
        }

        const csvContent = this.generateCSV(this.currentUserList);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `用户数据_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            toast.success('用户数据导出成功');
        } else {
            toast.error('浏览器不支持文件下载');
        }
    }

    // 生成CSV格式数据
    generateCSV(users) {
        const headers = ['用户ID', '手机号', '账户余额', '账户状态', '注册时间', '更新时间'];
        const csvRows = [headers.join(',')];

        users.forEach(user => {
            const row = [
                user.id,
                user.phone,
                ApiUtils.formatBalance(user.balance),
                ApiUtils.formatUserStatus(user.status).text,
                ApiUtils.formatDateTime(user.created_at),
                ApiUtils.formatDateTime(user.updated_at)
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    }
}

// 创建全局管理员管理器实例
const adminManager = new AdminManager();

// 在页面加载完成后初始化管理员功能
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保其他模块已加载
    setTimeout(() => {
        if (appState.isAdmin) {
            adminManager.init();
        }
    }, 1000);
});

// 添加CSS样式（通过JavaScript动态添加）
const adminStyles = `
    .action-buttons {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
    }

    .btn-sm {
        padding: 4px 8px;
        font-size: 12px;
    }

    .status-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-normal {
        background: #d4edda;
        color: #155724;
    }

    .status-frozen {
        background: #f8d7da;
        color: #721c24;
    }

    .status-unknown {
        background: #e2e3e5;
        color: #383d41;
    }

    .user-detail {
        margin: 20px 0;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-item label {
        font-weight: 500;
        color: #666;
    }

    .detail-item span {
        color: #333;
    }

    @media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
        }
        
        .data-table {
            font-size: 12px;
        }
        
        .data-table th,
        .data-table td {
            padding: 8px 4px;
        }
    }
`;

// 动态添加样式
const styleSheet = document.createElement('style');
styleSheet.textContent = adminStyles;
document.head.appendChild(styleSheet);
