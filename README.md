# 拍照搜题管理系统

基于S1.md API文档开发的Web管理系统，支持用户注册、登录和管理功能。

## 功能特性

### 🔐 用户认证
- 用户注册（手机号 + 验证码 + 密码 + 邀请码）
- 用户登录（手机号 + 密码）
- 自动保存登录状态

### 👤 用户管理
- 个人信息查看和编辑
- 密码修改功能
- 账户状态显示

### 📱 应用管理（新增）
- **应用创建**：支持创建新应用，自动生成密钥
- **应用列表**：网格布局展示所有应用
- **应用详情**：查看完整信息，管理密钥
- **状态管理**：冻结/恢复应用功能
- **密钥管理**：显示/隐藏、复制、重置密钥
- **安全控制**：数量限制、权限隔离、操作确认

### 🛡️ 权限控制
- **普通用户**：只能查看自己的信息和应用
- **管理员**：可以查看所有用户信息，进行用户管理

### 📊 管理功能（管理员专用）
- 用户列表查看
- 用户状态管理（冻结/解冻）
- 用户删除功能
- 数据导出功能

## 技术栈

- **前端**：纯HTML + CSS + JavaScript（无框架依赖）
- **API**：基于S1.md文档的RESTful API
- **样式**：响应式设计，支持移动端

## 文件结构

```
├── index.html                      # 主页面文件（包含应用管理）
├── styles.css                      # 样式文件（新增应用管理样式）
├── script.js                       # 主要JavaScript逻辑（新增应用管理功能）
├── api.js                          # API调用封装（新增应用管理API）
├── admin.js                        # 管理员功能模块
├── S1.md                          # API接口文档
├── test-app-management.html        # 应用管理功能测试页面
├── demo-app-management.html        # 应用管理功能演示页面
├── APPLICATION_MANAGEMENT_README.md # 应用管理实现说明
└── README.md                       # 说明文档
```

## 快速开始

### 1. 启动API服务
确保后端API服务运行在 `http://localhost:8080`

### 2. 打开管理系统
直接在浏览器中打开 `index.html` 文件

### 3. 体验应用管理功能
- **主应用**: `http://localhost:3000` - 完整的管理系统
- **功能测试**: `http://localhost:3000/test-app-management.html` - 测试各个组件
- **功能演示**: `http://localhost:3000/demo-app-management.html` - 交互式演示

### 3. 测试账户
- **管理员账户**：手机号 `13800138000`（登录后自动识别为管理员）
- **普通用户**：其他手机号

## 使用说明

### 用户注册
1. 点击"没有账户？立即注册"
2. 输入手机号
3. 点击"发送验证码"
4. 输入验证码、密码和邀请码（默认：SOLVE2024）
5. 点击"注册"

### 用户登录
1. 输入手机号和密码
2. 点击"登录"
3. 登录成功后进入管理界面

### 管理员功能
管理员登录后可以看到额外的菜单选项：
- **用户管理**：查看、编辑、冻结、删除用户
- **系统设置**：系统配置（开发中）
- **数据分析**：数据统计（开发中）

## API接口说明

系统基于以下API接口：

### 用户相关
- `POST /api/v1/user/register` - 用户注册
- `POST /api/v1/user/login` - 用户登录
- `GET /api/v1/user/profile/{user_id}` - 获取用户信息
- `PUT /api/v1/user/profile/{user_id}` - 更新用户信息
- `POST /api/v1/user/send-code` - 发送验证码

### 系统相关
- `GET /health` - 健康检查

## 权限说明

### 普通用户权限
- 查看个人信息
- 修改个人密码
- 查看自己创建的应用

### 管理员权限
- 所有普通用户权限
- 查看所有用户列表
- 管理用户状态（冻结/解冻）
- 删除用户
- 系统设置
- 数据分析

## 注意事项

1. **管理员识别**：目前通过手机号 `13800138000` 或用户ID为 `1` 来识别管理员
2. **数据持久化**：登录状态保存在浏览器本地存储中
3. **API依赖**：部分管理员功能需要后端提供相应的API接口
4. **验证码**：发送验证码有60秒冷却时间
5. **响应式**：支持移动端访问

## 开发说明

### 扩展功能
如需添加新功能，可以：
1. 在 `script.js` 中添加通用功能
2. 在 `admin.js` 中添加管理员专用功能
3. 在 `api.js` 中添加新的API调用
4. 在 `styles.css` 中添加样式

### 自定义管理员判断
修改 `script.js` 中的 `AppState.setCurrentUser()` 方法来自定义管理员判断逻辑。

### API接口扩展
当后端提供更多API接口时，可以在 `api.js` 中添加相应的方法。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 更新日志

### v1.0.0
- 基础用户认证功能
- 个人信息管理
- 管理员用户管理功能
- 响应式界面设计

## 联系方式

如有问题或建议，请参考S1.md文档或联系开发团队。
