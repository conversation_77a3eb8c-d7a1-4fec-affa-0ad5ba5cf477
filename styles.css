/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* 页面切换 */
.page {
    display: none;
}

.page.active {
    display: block;
}

/* 登录页面样式 */
#loginPage.active, #registerPage.active {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 24px;
}

.login-header p {
    color: #666;
    font-size: 14px;
}

.login-form {
    width: 100%;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.code-input-group {
    display: flex;
    gap: 10px;
}

.code-input-group input {
    flex: 1;
}

.send-code-btn {
    padding: 12px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
}

.send-code-btn:hover {
    background: #5a6fd8;
}

.send-code-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.login-btn:hover {
    background: #5a6fd8;
}

.login-footer {
    text-align: center;
    margin-top: 20px;
}

.login-footer a {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
}

.login-footer a:hover {
    text-decoration: underline;
}

/* 管理页面样式 */
.admin-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* 顶部导航 */
.admin-header {
    background: white;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.header-left h1 {
    font-size: 20px;
    color: #333;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.role-badge {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.role-badge.admin {
    background: #e74c3c;
}

.logout-btn {
    padding: 8px 16px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.logout-btn:hover {
    background: #c0392b;
}

/* 主体布局 */
.admin-container {
    display: flex;
    flex: 1;
}

.sidebar {
    width: 250px;
    background: white;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    padding-top: 20px;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #666;
    text-decoration: none;
    transition: all 0.3s;
}

.nav-item a:hover {
    background: #f8f9fa;
    color: #333;
}

.nav-item.active a {
    background: #667eea;
    color: white;
}

.nav-item .icon {
    margin-right: 10px;
    font-size: 16px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.content-panel {
    display: none;
}

.content-panel.active {
    display: block;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.panel-header h2 {
    font-size: 24px;
    color: #333;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* 仪表盘样式 */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
}

.stat-info h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
    font-size: 14px;
}

/* 个人信息样式 */
.profile-card {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-info {
    margin-bottom: 30px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 500;
    color: #666;
}

.info-item span {
    color: #333;
}

/* 表格样式 */
.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 500;
    color: #666;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* 空状态 */
.empty-state {
    text-align: center;
    color: #666;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 3000;
    transform: translateX(400px);
    transition: transform 0.3s;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #27ae60;
}

.toast.error {
    background: #e74c3c;
}

.toast.warning {
    background: #f39c12;
}

.toast.info {
    background: #3498db;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        order: 2;
    }
    
    .main-content {
        order: 1;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .login-container {
        margin: 20px;
        padding: 30px;
    }
}
