// 应用状态管理
class AppState {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.isAdmin = false;
        this.currentPage = 'login';
        this.currentTab = 'dashboard';
    }

    // 设置当前用户
    setCurrentUser(user) {
        this.currentUser = user;
        this.isLoggedIn = true;
        // 简单的管理员判断逻辑，可以根据实际需求修改
        this.isAdmin = user.phone === '13800138000' || user.id === 1;
        this.saveToStorage();
    }

    // 清除用户状态
    clearUser() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.isAdmin = false;
        this.removeFromStorage();
    }

    // 保存到本地存储
    saveToStorage() {
        localStorage.setItem('appState', JSON.stringify({
            currentUser: this.currentUser,
            isLoggedIn: this.isLoggedIn,
            isAdmin: this.isAdmin
        }));
    }

    // 从本地存储恢复
    loadFromStorage() {
        const saved = localStorage.getItem('appState');
        if (saved) {
            try {
                const state = JSON.parse(saved);
                this.currentUser = state.currentUser;
                this.isLoggedIn = state.isLoggedIn;
                this.isAdmin = state.isAdmin;
                return true;
            } catch (error) {
                console.error('恢复状态失败:', error);
            }
        }
        return false;
    }

    // 清除本地存储
    removeFromStorage() {
        localStorage.removeItem('appState');
    }
}

// 页面管理器
class PageManager {
    constructor() {
        this.pages = ['loginPage', 'registerPage', 'adminPage'];
        this.tabs = ['dashboard', 'profile', 'myapps', 'users', 'system', 'analytics'];
    }

    // 显示指定页面
    showPage(pageId) {
        console.log('切换到页面:', pageId);
        this.pages.forEach(id => {
            const page = document.getElementById(id);
            if (page) {
                const isActive = id === pageId;
                page.classList.toggle('active', isActive);
                console.log(`页面 ${id}: ${isActive ? '显示' : '隐藏'}`);
            } else {
                console.error(`未找到页面元素: ${id}`);
            }
        });
        appState.currentPage = pageId;
    }

    // 显示指定标签页
    showTab(tabId) {
        // 更新导航菜单
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.toggle('active', item.dataset.tab === tabId);
        });

        // 更新内容面板
        this.tabs.forEach(id => {
            const panel = document.getElementById(id);
            if (panel) {
                panel.classList.toggle('active', id === tabId);
            }
        });

        appState.currentTab = tabId;
    }

    // 更新管理员菜单显示
    updateAdminMenu() {
        const adminItems = document.querySelectorAll('.admin-only');
        const isAdmin = appState.isAdmin;
        
        adminItems.forEach(item => {
            item.style.display = isAdmin ? 'block' : 'none';
        });

        // 更新角色标识
        const roleElement = document.getElementById('userRole');
        if (roleElement) {
            roleElement.textContent = isAdmin ? '管理员' : '普通用户';
            roleElement.className = isAdmin ? 'role-badge admin' : 'role-badge';
        }
    }
}

// 消息提示管理器
class ToastManager {
    constructor() {
        this.toastElement = document.getElementById('toast');
    }

    show(message, type = 'info', duration = 3000) {
        if (!this.toastElement) return;

        this.toastElement.textContent = message;
        this.toastElement.className = `toast ${type}`;
        
        // 显示消息
        setTimeout(() => {
            this.toastElement.classList.add('show');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            this.hide();
        }, duration);
    }

    hide() {
        if (this.toastElement) {
            this.toastElement.classList.remove('show');
        }
    }

    success(message, duration) {
        this.show(message, 'success', duration);
    }

    error(message, duration) {
        this.show(message, 'error', duration);
    }

    warning(message, duration) {
        this.show(message, 'warning', duration);
    }

    info(message, duration) {
        this.show(message, 'info', duration);
    }
}

// 表单验证器
class FormValidator {
    static validateLoginForm(formData) {
        const errors = [];

        if (!ApiUtils.validatePhone(formData.phone)) {
            errors.push('请输入正确的手机号');
        }

        if (!ApiUtils.validatePassword(formData.password)) {
            errors.push('密码长度应为6-20位字符');
        }

        return errors;
    }

    static validateRegisterForm(formData) {
        const errors = [];

        if (!ApiUtils.validatePhone(formData.phone)) {
            errors.push('请输入正确的手机号');
        }

        if (!ApiUtils.validatePassword(formData.password)) {
            errors.push('密码长度应为6-20位字符');
        }

        if (!ApiUtils.validateCode(formData.code)) {
            errors.push('请输入6位数字验证码');
        }

        if (!formData.invite_code || formData.invite_code.trim() === '') {
            errors.push('请输入邀请码');
        }

        return errors;
    }
}

// 全局实例
const appState = new AppState();
const pageManager = new PageManager();
const toast = new ToastManager();

// 应用初始化
function initApp() {
    console.log('开始初始化应用...');
    console.log('appState:', appState);
    console.log('pageManager:', pageManager);
    console.log('toast:', toast);

    // 尝试从本地存储恢复状态
    if (appState.loadFromStorage() && appState.isLoggedIn) {
        console.log('从本地存储恢复登录状态');
        showAdminPage();
    } else {
        console.log('显示登录页面');
        pageManager.showPage('loginPage');
    }

    // 绑定事件监听器
    console.log('绑定事件监听器...');
    bindEventListeners();

    // 检查API服务状态
    console.log('检查API服务状态...');
    checkApiHealth();

    console.log('应用初始化完成');
}

// 绑定事件监听器
function bindEventListeners() {
    console.log('开始绑定事件监听器...');

    // 登录表单
    const loginForm = document.getElementById('loginForm');
    console.log('loginForm元素:', loginForm);
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
        console.log('登录表单事件已绑定');
    } else {
        console.error('未找到loginForm元素');
    }

    // 注册表单
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }

    // 发送验证码按钮
    const sendCodeBtn = document.getElementById('sendCodeBtn');
    if (sendCodeBtn) {
        sendCodeBtn.addEventListener('click', handleSendCode);
    }

    // 页面切换链接
    const showRegisterLink = document.getElementById('showRegister');
    const showLoginLink = document.getElementById('showLogin');

    console.log('showRegister元素:', showRegisterLink);
    console.log('showLogin元素:', showLoginLink);

    if (showRegisterLink) {
        showRegisterLink.addEventListener('click', (e) => {
            console.log('点击了注册链接');
            e.preventDefault();
            pageManager.showPage('registerPage');
        });
    }

    if (showLoginLink) {
        showLoginLink.addEventListener('click', (e) => {
            console.log('点击了登录链接');
            e.preventDefault();
            pageManager.showPage('loginPage');
        });
    }

    // 退出登录按钮
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }

    // 导航菜单
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const tabId = item.dataset.tab;
            if (tabId) {
                pageManager.showTab(tabId);
                loadTabContent(tabId);
            }
        });
    });

    // 编辑个人信息按钮
    const editProfileBtn = document.getElementById('editProfileBtn');
    if (editProfileBtn) {
        editProfileBtn.addEventListener('click', showEditProfileModal);
    }

    // 创建应用按钮
    const createAppBtn = document.getElementById('createAppBtn');
    if (createAppBtn) {
        createAppBtn.addEventListener('click', showCreateApplicationModal);
    }

    // 模态框关闭
    const modal = document.getElementById('modal');
    const closeBtn = modal?.querySelector('.close');
    if (closeBtn) {
        closeBtn.addEventListener('click', hideModal);
    }

    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hideModal();
            }
        });
    }
}

// 处理登录
async function handleLogin(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const loginData = {
        phone: formData.get('phone'),
        password: formData.get('password')
    };

    // 显示加载状态
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = '登录中...';

    // 表单验证
    const errors = FormValidator.validateLoginForm(loginData);
    if (errors.length > 0) {
        toast.error(errors[0]);
        resetSubmitButton(submitBtn, originalText);
        return { success: false, error: errors[0], type: 'validation' };
    }

    try {
        console.log('开始登录请求:', loginData.phone);
        const result = await apiService.login(loginData);

        if (result.success) {
            // 登录成功
            console.log('登录成功:', result.data);
            appState.setCurrentUser(result.data);

            // 显示成功消息
            const successMessage = ApiUtils.handleApiSuccess(result, '登录成功');
            toast.success(successMessage);

            // 跳转到管理页面
            setTimeout(() => {
                showAdminPage();
            }, 1000);

            const loginResult = {
                success: true,
                data: result.data,
                message: successMessage,
                type: 'login_success'
            };

            // 记录登录成功状态
            statusManager.recordStatus('用户登录', loginResult);
            return loginResult;
        } else {
            // 登录失败 - API返回错误
            console.log('登录失败:', result);
            const errorMessage = getLoginErrorMessage(result.code, result.error);
            toast.error(errorMessage);
            resetSubmitButton(submitBtn, originalText);

            const loginResult = {
                success: false,
                error: errorMessage,
                code: result.code,
                type: 'api_error'
            };

            // 记录登录失败状态
            statusManager.recordStatus('用户登录', loginResult);
            return loginResult;
        }
    } catch (error) {
        // 网络错误或其他异常
        console.error('登录异常:', error);
        const errorMessage = '网络连接失败，请检查网络后重试';
        toast.error(errorMessage);
        resetSubmitButton(submitBtn, originalText);

        const loginResult = {
            success: false,
            error: errorMessage,
            type: 'network_error',
            details: error.message
        };

        // 记录登录网络错误状态
        statusManager.recordStatus('用户登录', loginResult);
        return loginResult;
    }
}

// 处理注册
async function handleRegister(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const registerData = {
        phone: formData.get('phone'),
        password: formData.get('password'),
        code: formData.get('code'),
        invite_code: formData.get('invite_code')
    };

    // 显示加载状态
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = '注册中...';

    // 表单验证
    const errors = FormValidator.validateRegisterForm(registerData);
    if (errors.length > 0) {
        toast.error(errors[0]);
        resetSubmitButton(submitBtn, originalText);
        return { success: false, error: errors[0], type: 'validation' };
    }

    try {
        console.log('开始注册请求:', registerData.phone);
        const result = await apiService.register(registerData);

        if (result.success) {
            // 注册成功
            console.log('注册成功:', result.data);
            const successMessage = ApiUtils.handleApiSuccess(result, '注册成功，请使用新账户登录');
            toast.success(successMessage);

            // 清空注册表单
            e.target.reset();

            // 延迟跳转到登录页面
            setTimeout(() => {
                pageManager.showPage('loginPage');
                // 自动填入手机号
                const phoneInput = document.getElementById('phone');
                if (phoneInput) {
                    phoneInput.value = registerData.phone;
                }
            }, 2000);

            resetSubmitButton(submitBtn, originalText);

            const registerResult = {
                success: true,
                data: result.data,
                message: successMessage,
                type: 'register_success'
            };

            // 记录注册成功状态
            statusManager.recordStatus('用户注册', registerResult);
            return registerResult;
        } else {
            // 注册失败 - API返回错误
            console.log('注册失败:', result);
            const errorMessage = getRegisterErrorMessage(result.code, result.error);
            toast.error(errorMessage);
            resetSubmitButton(submitBtn, originalText);

            const registerResult = {
                success: false,
                error: errorMessage,
                code: result.code,
                type: 'api_error'
            };

            // 记录注册失败状态
            statusManager.recordStatus('用户注册', registerResult);
            return registerResult;
        }
    } catch (error) {
        // 网络错误或其他异常
        console.error('注册异常:', error);
        const errorMessage = '网络连接失败，请检查网络后重试';
        toast.error(errorMessage);
        resetSubmitButton(submitBtn, originalText);

        const registerResult = {
            success: false,
            error: errorMessage,
            type: 'network_error',
            details: error.message
        };

        // 记录注册网络错误状态
        statusManager.recordStatus('用户注册', registerResult);
        return registerResult;
    }
}

// 重置提交按钮状态
function resetSubmitButton(button, originalText) {
    if (button) {
        button.disabled = false;
        button.textContent = originalText;
    }
}

// 获取登录错误消息
function getLoginErrorMessage(code, defaultError) {
    const errorMessages = {
        400: '请求参数错误，请检查输入信息',
        401: '手机号或密码错误，请重新输入',
        403: '账户已被冻结，请联系管理员',
        404: '用户不存在，请先注册',
        429: '登录尝试过于频繁，请稍后再试',
        500: '服务器内部错误，请稍后重试',
        503: '服务暂时不可用，请稍后重试'
    };

    return errorMessages[code] || defaultError || '登录失败，请重试';
}

// 获取注册错误消息
function getRegisterErrorMessage(code, defaultError) {
    const errorMessages = {
        400: '请求参数错误，请检查输入信息',
        409: '手机号已注册，请直接登录或使用其他手机号',
        429: '注册请求过于频繁，请稍后再试',
        500: '服务器内部错误，请稍后重试',
        503: '服务暂时不可用，请稍后重试'
    };

    return errorMessages[code] || defaultError || '注册失败，请重试';
}

// 处理发送验证码
async function handleSendCode(e) {
    const phoneInput = document.getElementById('regPhone');
    const phone = phoneInput?.value;

    // 验证手机号
    if (!ApiUtils.validatePhone(phone)) {
        const errorMessage = '请输入正确的11位手机号';
        toast.error(errorMessage);
        return { success: false, error: errorMessage, type: 'validation' };
    }

    const button = e.target;
    const originalText = button.textContent;
    button.disabled = true;
    button.textContent = '发送中...';

    try {
        console.log('发送验证码到:', phone);
        const result = await apiService.sendVerificationCode(phone);

        if (result.success) {
            // 发送成功
            const successMessage = ApiUtils.handleApiSuccess(result, '验证码发送成功，请查收短信');
            toast.success(successMessage);
            startCountdown(button, originalText);

            const sendCodeResult = {
                success: true,
                message: successMessage,
                phone: phone,
                type: 'send_code_success'
            };

            // 记录发送验证码成功状态
            statusManager.recordStatus('发送验证码', sendCodeResult);
            return sendCodeResult;
        } else {
            // 发送失败 - API返回错误
            const errorMessage = getSendCodeErrorMessage(result.code, result.error);
            toast.error(errorMessage);
            resetSubmitButton(button, originalText);

            return {
                success: false,
                error: errorMessage,
                code: result.code,
                type: 'api_error'
            };
        }
    } catch (error) {
        // 网络错误或其他异常
        console.error('发送验证码异常:', error);
        const errorMessage = '网络连接失败，请检查网络后重试';
        toast.error(errorMessage);
        resetSubmitButton(button, originalText);

        return {
            success: false,
            error: errorMessage,
            type: 'network_error',
            details: error.message
        };
    }
}

// 获取发送验证码错误消息
function getSendCodeErrorMessage(code, defaultError) {
    const errorMessages = {
        400: '手机号格式错误，请检查后重试',
        429: '发送过于频繁，请60秒后再试',
        500: '服务器内部错误，请稍后重试',
        503: '短信服务暂时不可用，请稍后重试'
    };

    return errorMessages[code] || defaultError || '验证码发送失败，请重试';
}

// 验证码倒计时
function startCountdown(button, originalText, seconds = 60) {
    let remaining = seconds;

    const timer = setInterval(() => {
        button.textContent = `${remaining}秒后重试`;
        remaining--;

        if (remaining < 0) {
            clearInterval(timer);
            button.textContent = originalText;
            button.disabled = false;
        }
    }, 1000);
}

// 显示管理页面
function showAdminPage() {
    pageManager.showPage('adminPage');
    pageManager.updateAdminMenu();
    updateUserInfo();
    loadTabContent('dashboard');
}

// 更新用户信息显示
function updateUserInfo() {
    const user = appState.currentUser;
    if (!user) return;

    // 更新头部用户信息
    const currentUserElement = document.getElementById('currentUser');
    if (currentUserElement) {
        currentUserElement.textContent = user.phone;
    }

    // 更新个人信息页面
    updateProfileInfo(user);
}

// 更新个人信息页面
function updateProfileInfo(user) {
    const elements = {
        profileId: user.id,
        profilePhone: user.phone,
        profileBalance: ApiUtils.formatBalance(user.balance),
        profileStatus: ApiUtils.formatUserStatus(user.status).text,
        profileCreated: ApiUtils.formatDateTime(user.created_at),
        profileUpdated: ApiUtils.formatDateTime(user.updated_at)
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// 加载标签页内容
async function loadTabContent(tabId) {
    switch (tabId) {
        case 'dashboard':
            await loadDashboardData();
            break;
        case 'profile':
            await loadProfileData();
            break;
        case 'myapps':
            await loadApplicationsData();
            break;
        case 'users':
            if (appState.isAdmin) {
                await loadUsersData();
            }
            break;
        default:
            break;
    }
}

// 加载仪表盘数据
async function loadDashboardData() {
    // 这里可以调用相关API获取统计数据
    // 目前使用模拟数据
    document.getElementById('totalUsers').textContent = '1,234';
    document.getElementById('totalApps').textContent = '56';
    document.getElementById('totalBalance').textContent = '¥12,345.67';
}

// 加载个人信息数据
async function loadProfileData() {
    if (!appState.currentUser) return;

    try {
        const result = await apiService.getUserProfile(appState.currentUser.id);
        if (result.success) {
            updateProfileInfo(result.data);
        }
    } catch (error) {
        console.error('加载个人信息失败:', error);
    }
}

// 加载用户数据（管理员功能）
async function loadUsersData() {
    // 这个功能需要后端提供相应的API接口
    console.log('加载用户数据...');
}

// 处理退出登录
function handleLogout() {
    appState.clearUser();
    pageManager.showPage('loginPage');
    toast.info('已退出登录');
}

// 显示编辑个人信息模态框
function showEditProfileModal() {
    const modalBody = document.getElementById('modalBody');
    if (!modalBody) return;

    modalBody.innerHTML = `
        <h3>修改密码</h3>
        <form id="editPasswordForm">
            <div class="form-group">
                <label for="newPassword">新密码</label>
                <input type="password" id="newPassword" name="password" placeholder="6-20位字符" required>
            </div>
            <div class="form-group">
                <label for="confirmPassword">确认密码</label>
                <input type="password" id="confirmPassword" placeholder="再次输入新密码" required>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideModal()">取消</button>
                <button type="submit" class="btn btn-primary">确认修改</button>
            </div>
        </form>
    `;

    // 绑定表单提交事件
    const form = document.getElementById('editPasswordForm');
    if (form) {
        form.addEventListener('submit', handlePasswordUpdate);
    }

    showModal();
}

// 处理密码更新
async function handlePasswordUpdate(e) {
    e.preventDefault();
    
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (!ApiUtils.validatePassword(newPassword)) {
        toast.error('密码长度应为6-20位字符');
        return;
    }

    if (newPassword !== confirmPassword) {
        toast.error('两次输入的密码不一致');
        return;
    }

    try {
        const result = await apiService.updateUserProfile(appState.currentUser.id, {
            password: newPassword
        });

        if (result.success) {
            toast.success('密码修改成功');
            hideModal();
        } else {
            toast.error(ApiUtils.handleApiError(result, '密码修改失败'));
        }
    } catch (error) {
        console.error('密码修改错误:', error);
        toast.error('密码修改失败，请稍后重试');
    }
}

// 显示模态框
function showModal() {
    const modal = document.getElementById('modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

// 隐藏模态框
function hideModal() {
    const modal = document.getElementById('modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 状态显示管理器
class StatusManager {
    constructor() {
        this.statusHistory = [];
        this.maxHistory = 10;
    }

    // 记录状态
    recordStatus(operation, result) {
        const status = {
            timestamp: new Date().toISOString(),
            operation: operation,
            success: result.success,
            message: result.message || result.error,
            type: result.type,
            code: result.code,
            details: result.details
        };

        this.statusHistory.unshift(status);
        if (this.statusHistory.length > this.maxHistory) {
            this.statusHistory.pop();
        }

        // 输出到控制台
        console.log(`[${operation}] ${result.success ? '成功' : '失败'}:`, result);

        return status;
    }

    // 获取最近的状态
    getLastStatus(operation = null) {
        if (!operation) {
            return this.statusHistory[0] || null;
        }
        return this.statusHistory.find(status => status.operation === operation) || null;
    }

    // 获取状态历史
    getStatusHistory() {
        return [...this.statusHistory];
    }

    // 清除状态历史
    clearHistory() {
        this.statusHistory = [];
    }

    // 显示状态摘要
    showStatusSummary() {
        console.group('操作状态摘要');
        this.statusHistory.forEach((status, index) => {
            const icon = status.success ? '✅' : '❌';
            const time = new Date(status.timestamp).toLocaleTimeString();
            console.log(`${icon} [${time}] ${status.operation}: ${status.message}`);
        });
        console.groupEnd();
    }
}

// 创建全局状态管理器实例
const statusManager = new StatusManager();

// 检查API健康状态
async function checkApiHealth() {
    try {
        const health = await apiService.healthCheck();
        console.log('API健康状态:', health);

        const result = {
            success: health.status === 'ok',
            message: health.status === 'ok' ? 'API服务正常' : 'API服务异常',
            type: 'health_check',
            data: health
        };

        statusManager.recordStatus('健康检查', result);

        if (!result.success) {
            toast.warning('API服务连接异常，部分功能可能不可用');
        }

        return result;
    } catch (error) {
        console.error('API健康检查失败:', error);

        const result = {
            success: false,
            error: 'API服务连接失败',
            type: 'network_error',
            details: error.message
        };

        statusManager.recordStatus('健康检查', result);
        toast.warning('API服务连接异常，部分功能可能不可用');

        return result;
    }
}

// ========== 应用管理功能 ==========

// 应用管理状态
const applicationState = {
    applications: [],
    currentApplication: null,
    loading: false
};

// 加载应用数据
async function loadApplicationsData() {
    if (!appState.currentUser) return;

    const appsListElement = document.getElementById('appsList');
    const emptyStateElement = document.getElementById('appsEmptyState');
    const loadingElement = document.getElementById('appsLoading');

    // 显示加载状态
    applicationState.loading = true;
    appsListElement.style.display = 'none';
    emptyStateElement.style.display = 'none';
    loadingElement.style.display = 'block';

    try {
        const result = await apiService.getApplications(appState.currentUser.id);

        if (result.success) {
            applicationState.applications = result.data || [];
            renderApplicationsList();
        } else {
            console.error('获取应用列表失败:', result.error);
            toast.error('获取应用列表失败: ' + result.error);
            showEmptyState();
        }
    } catch (error) {
        console.error('获取应用列表异常:', error);
        toast.error('获取应用列表失败，请稍后重试');
        showEmptyState();
    } finally {
        applicationState.loading = false;
        loadingElement.style.display = 'none';
    }
}

// 渲染应用列表
function renderApplicationsList() {
    const appsListElement = document.getElementById('appsList');
    const emptyStateElement = document.getElementById('appsEmptyState');

    if (applicationState.applications.length === 0) {
        showEmptyState();
        return;
    }

    // 隐藏空状态，显示列表
    emptyStateElement.style.display = 'none';
    appsListElement.style.display = 'grid';

    // 生成应用卡片HTML
    appsListElement.innerHTML = applicationState.applications.map(app => createApplicationCard(app)).join('');
}

// 显示空状态
function showEmptyState() {
    const appsListElement = document.getElementById('appsList');
    const emptyStateElement = document.getElementById('appsEmptyState');

    appsListElement.style.display = 'none';
    emptyStateElement.style.display = 'block';
}

// 创建应用卡片HTML
function createApplicationCard(app) {
    const statusInfo = ApiUtils.formatApplicationStatus(app.status);
    const typeText = ApiUtils.formatApplicationType(app.type);
    const createdAt = ApiUtils.formatDateTime(app.created_at);
    const updatedAt = ApiUtils.formatDateTime(app.updated_at);

    return `
        <div class="app-card" data-app-id="${app.id}">
            <div class="app-card-header">
                <div class="app-info">
                    <h3>${escapeHtml(app.name)}</h3>
                    <div class="app-type">${typeText}</div>
                </div>
                <div class="app-status ${statusInfo.class}">${statusInfo.text}</div>
            </div>

            <div class="app-keys">
                <div class="key-item">
                    <div class="key-label">App Key</div>
                    <div class="key-value">
                        <span class="key-text">${ApiUtils.formatKeyDisplay(app.app_key)}</span>
                        <div class="key-actions">
                            <button class="key-btn copy" onclick="copyKey('${app.app_key}', 'App Key')" title="复制">📋</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="app-meta">
                创建时间: ${createdAt}<br>
                更新时间: ${updatedAt}
            </div>

            <div class="app-actions">
                <button class="btn btn-primary" onclick="showApplicationDetail(${app.id})">查看详情</button>
                <button class="btn btn-secondary" onclick="showEditApplicationModal(${app.id})">编辑</button>
                ${app.status === 1 ?
                    `<button class="btn btn-danger btn-sm" onclick="toggleApplicationStatus(${app.id}, 2)">冻结</button>` :
                    `<button class="btn btn-primary btn-sm" onclick="toggleApplicationStatus(${app.id}, 1)">恢复</button>`
                }
            </div>
        </div>
    `;
}

// 显示创建应用模态框
function showCreateApplicationModal() {
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>创建应用</h3>
        <form id="createAppForm" class="modal-form">
            <div class="form-group">
                <label for="appName">应用名称 *</label>
                <input type="text" id="appName" name="name" placeholder="请输入应用名称" required maxlength="50">
                <div class="form-help">1-50个字符</div>
            </div>
            <div class="form-group">
                <label for="appType">业务类型 *</label>
                <select id="appType" name="type" required>
                    <option value="1">拍照搜题</option>
                </select>
                <div class="form-help">应用类型创建后不可修改</div>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="hideModal()">取消</button>
                <button type="submit" class="btn btn-primary">创建应用</button>
            </div>
        </form>
    `;

    // 绑定表单提交事件
    const form = document.getElementById('createAppForm');
    form.addEventListener('submit', handleCreateApplication);

    showModal();
}

// 处理创建应用
async function handleCreateApplication(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
        name: formData.get('name').trim(),
        type: parseInt(formData.get('type'))
    };

    // 验证数据
    if (!ApiUtils.validateApplicationName(data.name)) {
        toast.error('应用名称必须为1-50个字符');
        return;
    }

    // 检查应用数量限制
    if (applicationState.applications.length >= 5) {
        toast.error('应用数量已达上限（5个），请删除不需要的应用后重试');
        return;
    }

    try {
        // 禁用提交按钮
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.textContent = '创建中...';

        const result = await apiService.createApplication(appState.currentUser.id, data);

        if (result.success) {
            toast.success('应用创建成功！');
            hideModal();

            // 显示密钥信息
            setTimeout(() => showApplicationCreatedModal(result.data), 300);

            // 刷新应用列表
            await loadApplicationsData();
        } else {
            if (result.code === 409) {
                toast.error('应用数量已达上限（5个），请删除不需要的应用后重试');
            } else if (result.code === 403) {
                toast.error('账户已被冻结，请联系管理员');
            } else {
                toast.error('创建应用失败: ' + result.error);
            }
        }
    } catch (error) {
        console.error('创建应用异常:', error);
        toast.error('创建应用失败，请稍后重试');
    } finally {
        // 恢复提交按钮
        const submitBtn = e.target.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = '创建应用';
        }
    }
}

// 显示应用创建成功模态框
function showApplicationCreatedModal(appData) {
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>应用创建成功</h3>
        <div class="security-warning">
            <span class="icon">⚠️</span>
            <strong>重要提醒：</strong>密钥信息只在此次显示，请妥善保存！
        </div>

        <div class="key-display-modal">
            <div class="key-label">应用名称</div>
            <div class="key-value">${escapeHtml(appData.name)}</div>
        </div>

        <div class="key-display-modal">
            <div class="key-label">App Key</div>
            <div class="key-value" id="createdAppKey">${appData.app_key}</div>
            <button class="btn btn-sm btn-primary" onclick="copyKey('${appData.app_key}', 'App Key')">复制 App Key</button>
        </div>

        <div class="key-display-modal">
            <div class="key-label">Secret Key</div>
            <div class="key-value" id="createdSecretKey">${appData.secret_key}</div>
            <button class="btn btn-sm btn-primary" onclick="copyKey('${appData.secret_key}', 'Secret Key')">复制 Secret Key</button>
        </div>

        <div class="modal-actions">
            <button class="btn btn-primary" onclick="hideModal()">我已保存密钥</button>
        </div>
    `;

    showModal();
}

// 显示应用详情模态框
async function showApplicationDetail(appId) {
    if (!appState.currentUser) return;

    try {
        const result = await apiService.getApplicationDetail(appState.currentUser.id, appId);

        if (result.success) {
            const app = result.data;
            applicationState.currentApplication = app;

            const statusInfo = ApiUtils.formatApplicationStatus(app.status);
            const typeText = ApiUtils.formatApplicationType(app.type);

            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <h3>应用详情</h3>

                <div class="key-display-modal">
                    <div class="key-label">应用名称</div>
                    <div class="key-value">${escapeHtml(app.name)}</div>
                </div>

                <div class="key-display-modal">
                    <div class="key-label">应用类型</div>
                    <div class="key-value">${typeText}</div>
                </div>

                <div class="key-display-modal">
                    <div class="key-label">应用状态</div>
                    <div class="key-value">
                        <span class="app-status ${statusInfo.class}">${statusInfo.text}</span>
                        <button class="btn btn-sm ${app.status === 1 ? 'btn-danger' : 'btn-primary'}"
                                onclick="toggleApplicationStatus(${app.id}, ${app.status === 1 ? 2 : 1})">
                            ${app.status === 1 ? '冻结应用' : '恢复应用'}
                        </button>
                    </div>
                </div>

                <div class="key-display-modal">
                    <div class="key-label">App Key</div>
                    <div class="key-value" id="detailAppKey">${app.app_key}</div>
                    <button class="btn btn-sm btn-primary" onclick="copyKey('${app.app_key}', 'App Key')">复制</button>
                </div>

                <div class="key-display-modal">
                    <div class="key-label">Secret Key</div>
                    <div class="key-value" id="detailSecretKey">********************************</div>
                    <div style="margin-top: 8px;">
                        <button class="btn btn-sm btn-secondary" onclick="toggleSecretKeyVisibility('${app.secret_key}')">显示</button>
                        <button class="btn btn-sm btn-primary" onclick="copyKey('${app.secret_key}', 'Secret Key')">复制</button>
                        <button class="btn btn-sm btn-warning" onclick="confirmResetSecretKey(${app.id})">重置密钥</button>
                    </div>
                </div>

                <div class="key-display-modal">
                    <div class="key-label">创建时间</div>
                    <div class="key-value">${ApiUtils.formatDateTime(app.created_at)}</div>
                </div>

                <div class="key-display-modal">
                    <div class="key-label">更新时间</div>
                    <div class="key-value">${ApiUtils.formatDateTime(app.updated_at)}</div>
                </div>

                <div class="modal-actions">
                    <button class="btn btn-secondary" onclick="showEditApplicationModal(${app.id})">编辑名称</button>
                    <button class="btn btn-primary" onclick="hideModal()">关闭</button>
                </div>
            `;

            showModal();
        } else {
            toast.error('获取应用详情失败: ' + result.error);
        }
    } catch (error) {
        console.error('获取应用详情异常:', error);
        toast.error('获取应用详情失败，请稍后重试');
    }
}

// 显示编辑应用模态框
async function showEditApplicationModal(appId) {
    const app = applicationState.applications.find(a => a.id === appId);
    if (!app) {
        toast.error('应用信息不存在');
        return;
    }

    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>编辑应用</h3>
        <form id="editAppForm" class="modal-form">
            <div class="form-group">
                <label for="editAppName">应用名称 *</label>
                <input type="text" id="editAppName" name="name" value="${escapeHtml(app.name)}"
                       placeholder="请输入应用名称" required maxlength="50">
                <div class="form-help">1-50个字符</div>
            </div>
            <div class="form-group">
                <label>应用类型</label>
                <div class="key-value">${ApiUtils.formatApplicationType(app.type)}</div>
                <div class="form-help">应用类型创建后不可修改</div>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="hideModal()">取消</button>
                <button type="submit" class="btn btn-primary">保存修改</button>
            </div>
        </form>
    `;

    // 绑定表单提交事件
    const form = document.getElementById('editAppForm');
    form.addEventListener('submit', (e) => handleEditApplication(e, appId));

    showModal();
}

// 处理编辑应用
async function handleEditApplication(e, appId) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
        name: formData.get('name').trim()
    };

    // 验证数据
    if (!ApiUtils.validateApplicationName(data.name)) {
        toast.error('应用名称必须为1-50个字符');
        return;
    }

    try {
        // 禁用提交按钮
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.textContent = '保存中...';

        const result = await apiService.updateApplication(appState.currentUser.id, appId, data);

        if (result.success) {
            toast.success('应用信息更新成功！');
            hideModal();

            // 刷新应用列表
            await loadApplicationsData();
        } else {
            toast.error('更新应用失败: ' + result.error);
        }
    } catch (error) {
        console.error('更新应用异常:', error);
        toast.error('更新应用失败，请稍后重试');
    } finally {
        // 恢复提交按钮
        const submitBtn = e.target.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = '保存修改';
        }
    }
}

// 切换应用状态
async function toggleApplicationStatus(appId, newStatus) {
    if (!appState.currentUser) return;

    const statusText = newStatus === 1 ? '恢复' : '冻结';
    const confirmMessage = `确定要${statusText}此应用吗？${newStatus === 2 ? '冻结后应用将无法调用API服务。' : ''}`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        const result = await apiService.updateApplicationStatus(appState.currentUser.id, appId, newStatus);

        if (result.success) {
            toast.success(`应用${statusText}成功！`);

            // 刷新应用列表
            await loadApplicationsData();

            // 如果当前显示的是详情模态框，也需要更新
            if (applicationState.currentApplication && applicationState.currentApplication.id === appId) {
                hideModal();
                setTimeout(() => showApplicationDetail(appId), 300);
            }
        } else {
            toast.error(`${statusText}应用失败: ` + result.error);
        }
    } catch (error) {
        console.error(`${statusText}应用异常:`, error);
        toast.error(`${statusText}应用失败，请稍后重试`);
    }
}

// 确认重置密钥
function confirmResetSecretKey(appId) {
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <div class="confirm-dialog">
            <div class="icon">⚠️</div>
            <h3>确认重置密钥</h3>
            <p>重置密钥后，旧密钥将立即失效，请确保已更新客户端配置。</p>
            <p><strong>此操作不可撤销！</strong></p>
            <div class="confirm-actions">
                <button class="btn btn-secondary" onclick="showApplicationDetail(${appId})">取消</button>
                <button class="btn btn-danger" onclick="resetSecretKey(${appId})">确认重置</button>
            </div>
        </div>
    `;
}

// 重置密钥
async function resetSecretKey(appId) {
    if (!appState.currentUser) return;

    try {
        const result = await apiService.resetSecretKey(appState.currentUser.id, appId);

        if (result.success) {
            toast.success('密钥重置成功！请及时更新客户端配置。');

            // 显示新密钥
            showSecretKeyResetModal(result.data);

            // 刷新应用列表
            await loadApplicationsData();
        } else {
            toast.error('重置密钥失败: ' + result.error);
        }
    } catch (error) {
        console.error('重置密钥异常:', error);
        toast.error('重置密钥失败，请稍后重试');
    }
}

// 显示密钥重置成功模态框
function showSecretKeyResetModal(appData) {
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>密钥重置成功</h3>
        <div class="security-warning">
            <span class="icon">⚠️</span>
            <strong>重要提醒：</strong>新密钥信息只在此次显示，请妥善保存！旧密钥已失效。
        </div>

        <div class="key-display-modal">
            <div class="key-label">应用名称</div>
            <div class="key-value">${escapeHtml(appData.name)}</div>
        </div>

        <div class="key-display-modal">
            <div class="key-label">App Key（未变更）</div>
            <div class="key-value">${appData.app_key}</div>
            <button class="btn btn-sm btn-primary" onclick="copyKey('${appData.app_key}', 'App Key')">复制</button>
        </div>

        <div class="key-display-modal">
            <div class="key-label">新 Secret Key</div>
            <div class="key-value">${appData.secret_key}</div>
            <button class="btn btn-sm btn-primary" onclick="copyKey('${appData.secret_key}', 'Secret Key')">复制</button>
        </div>

        <div class="modal-actions">
            <button class="btn btn-primary" onclick="hideModal()">我已保存新密钥</button>
        </div>
    `;

    showModal();
}

// 切换密钥可见性
function toggleSecretKeyVisibility(secretKey) {
    const element = document.getElementById('detailSecretKey');
    const button = event.target;

    if (element.textContent === '********************************') {
        element.textContent = secretKey;
        button.textContent = '隐藏';
    } else {
        element.textContent = '********************************';
        button.textContent = '显示';
    }
}

// 复制密钥到剪贴板
async function copyKey(key, keyType) {
    try {
        const success = await ApiUtils.copyToClipboard(key);
        if (success) {
            toast.success(`${keyType} 已复制到剪贴板`);
        } else {
            toast.error('复制失败，请手动复制');
        }
    } catch (error) {
        console.error('复制失败:', error);
        toast.error('复制失败，请手动复制');
    }
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 显示应用详情模态框（用于测试）
function showApplicationDetailModal(app) {
    const statusInfo = ApiUtils.formatApplicationStatus(app.status);
    const typeText = ApiUtils.formatApplicationType(app.type);

    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>应用详情</h3>

        <div class="key-display-modal">
            <div class="key-label">应用名称</div>
            <div class="key-value">${escapeHtml(app.name)}</div>
        </div>

        <div class="key-display-modal">
            <div class="key-label">应用类型</div>
            <div class="key-value">${typeText}</div>
        </div>

        <div class="key-display-modal">
            <div class="key-label">应用状态</div>
            <div class="key-value">
                <span class="app-status ${statusInfo.class}">${statusInfo.text}</span>
                <button class="btn btn-sm ${app.status === 1 ? 'btn-danger' : 'btn-primary'}"
                        onclick="toggleApplicationStatus(${app.id}, ${app.status === 1 ? 2 : 1})">
                    ${app.status === 1 ? '冻结应用' : '恢复应用'}
                </button>
            </div>
        </div>

        <div class="key-display-modal">
            <div class="key-label">App Key</div>
            <div class="key-value" id="detailAppKey">${app.app_key}</div>
            <button class="btn btn-sm btn-primary" onclick="copyKey('${app.app_key}', 'App Key')">复制</button>
        </div>

        <div class="key-display-modal">
            <div class="key-label">Secret Key</div>
            <div class="key-value" id="detailSecretKey">********************************</div>
            <div style="margin-top: 8px;">
                <button class="btn btn-sm btn-secondary" onclick="toggleSecretKeyVisibility('${app.secret_key}')">显示</button>
                <button class="btn btn-sm btn-primary" onclick="copyKey('${app.secret_key}', 'Secret Key')">复制</button>
                <button class="btn btn-sm btn-warning" onclick="confirmResetSecretKey(${app.id})">重置密钥</button>
            </div>
        </div>

        <div class="key-display-modal">
            <div class="key-label">创建时间</div>
            <div class="key-value">${ApiUtils.formatDateTime(app.created_at)}</div>
        </div>

        <div class="key-display-modal">
            <div class="key-label">更新时间</div>
            <div class="key-value">${ApiUtils.formatDateTime(app.updated_at)}</div>
        </div>

        <div class="modal-actions">
            <button class="btn btn-secondary" onclick="showEditApplicationModal(${app.id})">编辑名称</button>
            <button class="btn btn-primary" onclick="hideModal()">关闭</button>
        </div>
    `;

    showModal();
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始初始化应用...');
    initApp();
});
