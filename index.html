<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拍照搜题管理系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="page active">
        <div class="login-container">
            <div class="login-header">
                <h1>拍照搜题管理系统</h1>
                <p>请登录您的账户</p>
            </div>
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="phone">手机号</label>
                    <input type="tel" id="phone" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                </div>
                <button type="submit" class="login-btn">登录</button>
            </form>
            <div class="login-footer">
                <a href="#" id="showRegister">没有账户？立即注册</a>
                <a href="#" id="showForgotPassword">忘记密码？</a>
            </div>
        </div>
    </div>

    <!-- 注册页面 -->
    <div id="registerPage" class="page">
        <div class="login-container">
            <div class="login-header">
                <h1>用户注册</h1>
                <p>创建您的账户</p>
            </div>
            <form id="registerForm" class="login-form">
                <div class="form-group">
                    <label for="regPhone">手机号</label>
                    <input type="tel" id="regPhone" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <div class="form-group">
                    <label for="regPassword">密码</label>
                    <input type="password" id="regPassword" name="password" placeholder="6-20位字符" required>
                </div>
                <div class="form-group">
                    <label for="verifyCode">验证码</label>
                    <div class="code-input-group">
                        <input type="text" id="verifyCode" name="code" placeholder="请输入验证码" required>
                        <button type="button" id="sendCodeBtn" class="send-code-btn">发送验证码</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="inviteCode">邀请码</label>
                    <input type="text" id="inviteCode" name="invite_code" placeholder="请输入邀请码" value="SOLVE2024" required>
                </div>
                <button type="submit" class="login-btn">注册</button>
            </form>
            <div class="login-footer">
                <a href="#" id="showLogin">已有账户？立即登录</a>
            </div>
        </div>
    </div>

    <!-- 忘记密码页面 -->
    <div id="forgotPasswordPage" class="page">
        <div class="login-container">
            <div class="login-header">
                <h1>忘记密码</h1>
                <p>请输入您的手机号，我们将发送验证码</p>
            </div>
            <form id="forgotPasswordForm" class="login-form">
                <div class="form-group">
                    <label for="forgotPhone">手机号</label>
                    <input type="tel" id="forgotPhone" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <div class="form-group">
                    <label for="forgotCode">验证码</label>
                    <div class="code-input-group">
                        <input type="text" id="forgotCode" name="code" placeholder="请输入6位验证码" required maxlength="6">
                        <button type="button" id="sendForgotCodeBtn" class="send-code-btn">发送验证码</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="newPassword">新密码</label>
                    <input type="password" id="newPassword" name="new_password" placeholder="请输入新密码（6-20位）" required>
                    <div class="password-strength" id="passwordStrength"></div>
                </div>
                <div class="form-group">
                    <label for="confirmNewPassword">确认密码</label>
                    <input type="password" id="confirmNewPassword" name="confirm_password" placeholder="请再次输入新密码" required>
                </div>
                <button type="submit" class="login-btn">重置密码</button>
            </form>
            <div class="login-footer">
                <a href="#" id="backToLogin">返回登录</a>
            </div>
        </div>
    </div>

    <!-- 管理页面 -->
    <div id="adminPage" class="page">
        <div class="admin-container">
            <!-- 顶部导航 -->
            <header class="admin-header">
                <div class="header-left">
                    <h1>拍照搜题管理系统</h1>
                </div>
                <div class="header-right">
                    <span class="user-info">
                        <span id="currentUser">用户</span>
                        <span id="userRole" class="role-badge">普通用户</span>
                    </span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </header>

            <!-- 侧边栏导航 -->
            <nav class="sidebar">
                <ul class="nav-menu">
                    <li class="nav-item active" data-tab="dashboard">
                        <a href="#"><i class="icon">📊</i>仪表盘</a>
                    </li>
                    <li class="nav-item" data-tab="profile">
                        <a href="#"><i class="icon">👤</i>个人信息</a>
                    </li>
                    <li class="nav-item" data-tab="myapps">
                        <a href="#"><i class="icon">📱</i>我的应用</a>
                    </li>
                    <!-- 管理员专用菜单 -->
                    <li class="nav-item admin-only" data-tab="users" style="display: none;">
                        <a href="#"><i class="icon">👥</i>用户管理</a>
                    </li>
                    <li class="nav-item admin-only" data-tab="system" style="display: none;">
                        <a href="#"><i class="icon">⚙️</i>系统设置</a>
                    </li>
                    <li class="nav-item admin-only" data-tab="analytics" style="display: none;">
                        <a href="#"><i class="icon">📈</i>数据分析</a>
                    </li>
                </ul>
            </nav>

            <!-- 主内容区域 -->
            <main class="main-content">
                <!-- 仪表盘 -->
                <div id="dashboard" class="content-panel active">
                    <div class="panel-header">
                        <h2>仪表盘</h2>
                    </div>
                    <div class="dashboard-stats">
                        <div class="stat-card">
                            <div class="stat-icon">👤</div>
                            <div class="stat-info">
                                <h3 id="totalUsers">-</h3>
                                <p>总用户数</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📱</div>
                            <div class="stat-info">
                                <h3 id="totalApps">-</h3>
                                <p>应用总数</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">💰</div>
                            <div class="stat-info">
                                <h3 id="totalBalance">-</h3>
                                <p>总余额</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 个人信息 -->
                <div id="profile" class="content-panel">
                    <div class="panel-header">
                        <h2>个人信息</h2>
                    </div>
                    <div class="profile-content">
                        <div class="profile-card">
                            <div class="profile-info">
                                <div class="info-item">
                                    <label>用户ID:</label>
                                    <span id="profileId">-</span>
                                </div>
                                <div class="info-item">
                                    <label>手机号:</label>
                                    <span id="profilePhone">-</span>
                                </div>
                                <div class="info-item">
                                    <label>账户余额:</label>
                                    <span id="profileBalance">-</span>
                                </div>
                                <div class="info-item">
                                    <label>账户状态:</label>
                                    <span id="profileStatus">-</span>
                                </div>
                                <div class="info-item">
                                    <label>注册时间:</label>
                                    <span id="profileCreated">-</span>
                                </div>
                                <div class="info-item">
                                    <label>更新时间:</label>
                                    <span id="profileUpdated">-</span>
                                </div>
                            </div>
                            <div class="profile-actions">
                                <button id="changePasswordBtn" class="btn btn-primary">修改密码</button>
                                <button id="editProfileBtn" class="btn btn-secondary">编辑信息</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 我的应用 -->
                <div id="myapps" class="content-panel">
                    <div class="panel-header">
                        <h2>我的应用</h2>
                        <button id="createAppBtn" class="btn btn-primary">创建应用</button>
                    </div>
                    <div class="apps-content">
                        <!-- 应用列表容器 -->
                        <div id="appsList" class="apps-list">
                            <!-- 应用卡片将通过JavaScript动态生成 -->
                        </div>
                        <!-- 空状态 -->
                        <div id="appsEmptyState" class="empty-state" style="display: none;">
                            <div class="empty-icon">📱</div>
                            <h3>暂无应用</h3>
                            <p>您还没有创建任何应用，点击上方"创建应用"按钮开始创建您的第一个应用</p>
                            <button class="btn btn-primary" onclick="showCreateApplicationModal()">创建应用</button>
                        </div>
                        <!-- 加载状态 -->
                        <div id="appsLoading" class="loading-state">
                            <div class="loading-spinner"></div>
                            <p>正在加载应用列表...</p>
                        </div>
                    </div>
                </div>

                <!-- 用户管理（管理员专用） -->
                <div id="users" class="content-panel">
                    <div class="panel-header">
                        <h2>用户管理</h2>
                        <button class="btn btn-primary">添加用户</button>
                    </div>
                    <div class="users-content">
                        <div class="table-container">
                            <table id="usersTable" class="data-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>手机号</th>
                                        <th>余额</th>
                                        <th>状态</th>
                                        <th>注册时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 用户数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 系统设置（管理员专用） -->
                <div id="system" class="content-panel">
                    <div class="panel-header">
                        <h2>系统设置</h2>
                    </div>
                    <div class="system-content">
                        <p class="empty-state">系统设置功能开发中...</p>
                    </div>
                </div>

                <!-- 数据分析（管理员专用） -->
                <div id="analytics" class="content-panel">
                    <div class="panel-header">
                        <h2>数据分析</h2>
                    </div>
                    <div class="analytics-content">
                        <p class="empty-state">数据分析功能开发中...</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast"></div>

    <script src="api.js"></script>
    <script src="script.js"></script>
    <script src="admin.js"></script>
</body>
</html>
