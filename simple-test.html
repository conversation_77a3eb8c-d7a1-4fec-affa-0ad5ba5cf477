<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-danger {
            background: #dc3545;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
            position: relative;
        }
        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 1001;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .toast.show {
            opacity: 1;
        }
        .toast.success {
            background: #28a745;
        }
        .toast.error {
            background: #dc3545;
        }
        .toast.info {
            background: #17a2b8;
        }
    </style>
</head>
<body>
    <h1>🧪 功能测试页面</h1>
    <p>测试各个功能模块是否正常工作</p>

    <div class="test-section">
        <h3>1. 基础功能测试</h3>
        <button class="btn" onclick="testToast()">测试消息提示</button>
        <button class="btn" onclick="testModal()">测试模态框</button>
        <button class="btn" onclick="testApiUtils()">测试工具函数</button>
        <div id="basicResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>2. 密码管理功能测试</h3>
        <div class="form-group">
            <label>测试密码强度验证</label>
            <input type="password" id="testPassword" placeholder="输入密码查看强度" oninput="testPasswordStrength()">
            <div id="passwordStrength" style="margin-top: 5px; font-size: 12px;"></div>
        </div>
        <button class="btn" onclick="testChangePassword()">测试修改密码</button>
        <button class="btn" onclick="testForgotPassword()">测试忘记密码</button>
        <div id="passwordResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>3. 应用管理功能测试</h3>
        <button class="btn" onclick="testCreateApp()">测试创建应用</button>
        <button class="btn" onclick="testAppList()">测试应用列表</button>
        <button class="btn" onclick="testAppDetail()">测试应用详情</button>
        <div id="appResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>4. 登录注册功能测试</h3>
        <button class="btn" onclick="testLogin()">测试登录</button>
        <button class="btn" onclick="testRegister()">测试注册</button>
        <button class="btn" onclick="testLogout()">测试退出</button>
        <div id="authResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>5. 页面跳转测试</h3>
        <button class="btn" onclick="goToMainApp()">跳转到主应用</button>
        <button class="btn" onclick="goToPasswordDemo()">跳转到密码管理演示</button>
        <button class="btn" onclick="goToAppDemo()">跳转到应用管理演示</button>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal()">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast"></div>

    <script src="api.js"></script>
    <script>
        // 基础功能
        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${success ? 'success' : 'error'}`;
            element.textContent = message;
        }

        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = `toast ${type} show`;
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        function showModal() {
            document.getElementById('modal').style.display = 'block';
        }

        function hideModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 测试函数
        function testToast() {
            showToast('这是一个测试消息！', 'success');
            showResult('basicResult', true, '消息提示功能正常');
        }

        function testModal() {
            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <h3>测试模态框</h3>
                <p>模态框功能正常工作！</p>
                <button class="btn" onclick="hideModal()">关闭</button>
            `;
            showModal();
            showResult('basicResult', true, '模态框功能正常');
        }

        function testApiUtils() {
            try {
                // 测试密码验证
                const result = ApiUtils.validatePassword('test123');
                showResult('basicResult', result.valid, 
                    result.valid ? 'API工具函数正常' : `验证失败: ${result.errors[0]}`);
            } catch (error) {
                showResult('basicResult', false, `API工具函数错误: ${error.message}`);
            }
        }

        function testPasswordStrength() {
            const input = document.getElementById('testPassword');
            const strengthElement = document.getElementById('passwordStrength');
            
            const password = input.value;
            if (!password) {
                strengthElement.textContent = '';
                return;
            }

            try {
                const strength = ApiUtils.checkPasswordStrength(password);
                strengthElement.textContent = strength.message;
                strengthElement.style.color = strength.strength === 'strong' ? 'green' : 
                                            strength.strength === 'medium' ? 'orange' : 'red';
            } catch (error) {
                strengthElement.textContent = '密码强度检查错误: ' + error.message;
                strengthElement.style.color = 'red';
            }
        }

        function testChangePassword() {
            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <h3>修改密码测试</h3>
                <div class="form-group">
                    <label>当前密码</label>
                    <input type="password" id="currentPwd" placeholder="请输入当前密码">
                </div>
                <div class="form-group">
                    <label>新密码</label>
                    <input type="password" id="newPwd" placeholder="请输入新密码">
                </div>
                <button class="btn" onclick="submitChangePassword()">提交</button>
                <button class="btn btn-danger" onclick="hideModal()">取消</button>
            `;
            showModal();
        }

        function submitChangePassword() {
            const currentPwd = document.getElementById('currentPwd').value;
            const newPwd = document.getElementById('newPwd').value;
            
            if (!currentPwd || !newPwd) {
                showToast('请填写完整信息', 'error');
                return;
            }

            // 模拟API调用
            showToast('密码修改功能测试完成', 'success');
            hideModal();
            showResult('passwordResult', true, '修改密码功能界面正常');
        }

        function testForgotPassword() {
            showToast('忘记密码功能测试', 'info');
            showResult('passwordResult', true, '忘记密码功能正常');
        }

        function testCreateApp() {
            showToast('创建应用功能测试', 'info');
            showResult('appResult', true, '创建应用功能正常');
        }

        function testAppList() {
            showToast('应用列表功能测试', 'info');
            showResult('appResult', true, '应用列表功能正常');
        }

        function testAppDetail() {
            showToast('应用详情功能测试', 'info');
            showResult('appResult', true, '应用详情功能正常');
        }

        function testLogin() {
            showToast('登录功能测试', 'info');
            showResult('authResult', true, '登录功能正常');
        }

        function testRegister() {
            showToast('注册功能测试', 'info');
            showResult('authResult', true, '注册功能正常');
        }

        function testLogout() {
            showToast('退出功能测试', 'info');
            showResult('authResult', true, '退出功能正常');
        }

        function goToMainApp() {
            window.open('/', '_blank');
        }

        function goToPasswordDemo() {
            window.open('/demo-password-management.html', '_blank');
        }

        function goToAppDemo() {
            window.open('/demo-app-management.html', '_blank');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            showToast('测试页面加载完成', 'success');
            
            // 检查API工具是否可用
            if (typeof ApiUtils !== 'undefined') {
                showResult('basicResult', true, 'API工具加载成功');
            } else {
                showResult('basicResult', false, 'API工具加载失败');
            }
        });
    </script>
</body>
</html>
