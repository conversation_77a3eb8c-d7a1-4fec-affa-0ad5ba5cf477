<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作演示 - 密码管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .demo-login {
            max-width: 400px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .demo-admin {
            display: none;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .demo-tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }
        .demo-tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        .demo-tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .demo-content {
            display: none;
        }
        .demo-content.active {
            display: block;
        }
        .demo-form {
            max-width: 400px;
        }
        .demo-form .form-group {
            margin-bottom: 15px;
        }
        .demo-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .demo-form input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .demo-form button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .demo-form button:hover {
            background: #0056b3;
        }
        .demo-form button.secondary {
            background: #6c757d;
        }
        .demo-form button.danger {
            background: #dc3545;
        }
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }
        .password-strength.weak { color: #dc3545; }
        .password-strength.medium { color: #ffc107; }
        .password-strength.strong { color: #28a745; }
        .demo-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .demo-result.success {
            background: #d4edda;
            color: #155724;
        }
        .demo-result.error {
            background: #f8d7da;
            color: #721c24;
        }
        .app-card-demo {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .app-card-demo h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .app-card-demo .app-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .app-card-demo .app-actions {
            display: flex;
            gap: 8px;
        }
        .app-card-demo .app-actions button {
            font-size: 12px;
            padding: 4px 8px;
        }
    </style>
</head>
<body>
    <!-- 登录界面 -->
    <div id="loginSection" class="demo-login">
        <h2>密码管理系统演示</h2>
        <p>演示登录和密码管理功能</p>
        
        <div class="demo-form">
            <div class="form-group">
                <label>手机号</label>
                <input type="tel" id="demoPhone" value="13800138000" placeholder="请输入手机号">
            </div>
            <div class="form-group">
                <label>密码</label>
                <input type="password" id="demoPassword" value="test123" placeholder="请输入密码">
            </div>
            <button onclick="demoLogin()">登录</button>
            <button onclick="showForgotPassword()" class="secondary">忘记密码</button>
        </div>
        
        <div id="loginResult" class="demo-result" style="display: none;"></div>
    </div>

    <!-- 忘记密码界面 -->
    <div id="forgotSection" class="demo-login" style="display: none;">
        <h2>忘记密码</h2>
        <p>通过手机验证码重置密码</p>
        
        <div class="demo-form">
            <div class="form-group">
                <label>手机号</label>
                <input type="tel" id="forgotPhone" value="13800138000" placeholder="请输入手机号">
            </div>
            <div class="form-group">
                <label>验证码</label>
                <div style="display: flex; gap: 10px;">
                    <input type="text" id="forgotCode" placeholder="请输入验证码" style="flex: 1;">
                    <button id="sendCodeBtn" onclick="sendVerificationCode()">发送验证码</button>
                </div>
            </div>
            <div class="form-group">
                <label>新密码</label>
                <input type="password" id="forgotNewPassword" placeholder="请输入新密码" oninput="checkPasswordStrength()">
                <div id="forgotPasswordStrength" class="password-strength"></div>
            </div>
            <div class="form-group">
                <label>确认密码</label>
                <input type="password" id="forgotConfirmPassword" placeholder="请再次输入新密码">
            </div>
            <button onclick="resetPassword()">重置密码</button>
            <button onclick="backToLogin()" class="secondary">返回登录</button>
        </div>
        
        <div id="forgotResult" class="demo-result" style="display: none;"></div>
    </div>

    <!-- 管理界面 -->
    <div id="adminSection" class="demo-admin">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2>管理系统</h2>
            <button onclick="demoLogout()" class="secondary">退出登录</button>
        </div>
        
        <div class="demo-tabs">
            <div class="demo-tab active" onclick="showTab('profile')">个人信息</div>
            <div class="demo-tab" onclick="showTab('password')">密码管理</div>
            <div class="demo-tab" onclick="showTab('apps')">应用管理</div>
        </div>

        <!-- 个人信息 -->
        <div id="profileContent" class="demo-content active">
            <h3>个人信息</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 4px;">
                <p><strong>用户ID:</strong> 1</p>
                <p><strong>手机号:</strong> 13800138000</p>
                <p><strong>账户状态:</strong> <span style="color: green;">正常</span></p>
                <p><strong>注册时间:</strong> 2024-01-01 12:00:00</p>
            </div>
        </div>

        <!-- 密码管理 -->
        <div id="passwordContent" class="demo-content">
            <h3>修改密码</h3>
            <div class="demo-form">
                <div class="form-group">
                    <label>当前密码</label>
                    <input type="password" id="currentPassword" placeholder="请输入当前密码">
                </div>
                <div class="form-group">
                    <label>新密码</label>
                    <input type="password" id="newPassword" placeholder="请输入新密码" oninput="checkNewPasswordStrength()">
                    <div id="newPasswordStrength" class="password-strength"></div>
                </div>
                <div class="form-group">
                    <label>确认密码</label>
                    <input type="password" id="confirmPassword" placeholder="请再次输入新密码">
                </div>
                <button onclick="changePassword()">修改密码</button>
            </div>
            <div id="passwordResult" class="demo-result" style="display: none;"></div>
        </div>

        <!-- 应用管理 -->
        <div id="appsContent" class="demo-content">
            <h3>我的应用</h3>
            <button onclick="createApp()" style="margin-bottom: 15px;">创建应用</button>
            
            <div id="appsList">
                <div class="app-card-demo">
                    <h4>我的搜题应用</h4>
                    <div class="app-meta">
                        类型: 拍照搜题 | 状态: <span style="color: green;">正常</span> | 创建时间: 2024-01-01
                    </div>
                    <div class="app-actions">
                        <button onclick="viewAppDetail(1)">查看详情</button>
                        <button onclick="editApp(1)" class="secondary">编辑</button>
                        <button onclick="freezeApp(1)" class="danger">冻结</button>
                    </div>
                </div>
            </div>
            <div id="appsResult" class="demo-result" style="display: none;"></div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal()">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast"></div>

    <script src="api.js"></script>
    <script>
        let currentUser = null;
        let countdownTimer = null;

        // 基础功能
        function showResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `demo-result ${success ? 'success' : 'error'}`;
            element.textContent = message;
        }

        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = `toast ${type} show`;
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        function showModal() {
            document.getElementById('modal').style.display = 'block';
        }

        function hideModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 页面切换
        function showSection(sectionId) {
            ['loginSection', 'forgotSection', 'adminSection'].forEach(id => {
                document.getElementById(id).style.display = id === sectionId ? 'block' : 'none';
            });
        }

        function showTab(tabId) {
            // 更新标签状态
            document.querySelectorAll('.demo-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新内容
            document.querySelectorAll('.demo-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabId + 'Content').classList.add('active');
        }

        // 登录功能
        function demoLogin() {
            const phone = document.getElementById('demoPhone').value;
            const password = document.getElementById('demoPassword').value;

            if (!phone || !password) {
                showResult('loginResult', false, '请填写完整的登录信息');
                return;
            }

            // 模拟登录
            currentUser = { id: 1, phone: phone };
            showResult('loginResult', true, '登录成功！');
            
            setTimeout(() => {
                showSection('adminSection');
                showToast('欢迎使用密码管理系统', 'success');
            }, 1000);
        }

        function demoLogout() {
            currentUser = null;
            showSection('loginSection');
            showToast('已退出登录', 'info');
        }

        // 忘记密码功能
        function showForgotPassword() {
            showSection('forgotSection');
        }

        function backToLogin() {
            showSection('loginSection');
        }

        function sendVerificationCode() {
            const phone = document.getElementById('forgotPhone').value;
            if (!phone) {
                showResult('forgotResult', false, '请输入手机号');
                return;
            }

            showResult('forgotResult', true, '验证码发送成功！（演示：123456）');
            startCountdown();
        }

        function startCountdown() {
            const btn = document.getElementById('sendCodeBtn');
            let count = 60;
            btn.disabled = true;
            
            countdownTimer = setInterval(() => {
                if (count <= 0) {
                    clearInterval(countdownTimer);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                } else {
                    btn.textContent = `${count}秒后重试`;
                    count--;
                }
            }, 1000);
        }

        function resetPassword() {
            const phone = document.getElementById('forgotPhone').value;
            const code = document.getElementById('forgotCode').value;
            const newPassword = document.getElementById('forgotNewPassword').value;
            const confirmPassword = document.getElementById('forgotConfirmPassword').value;

            if (!phone || !code || !newPassword || !confirmPassword) {
                showResult('forgotResult', false, '请填写完整信息');
                return;
            }

            if (newPassword !== confirmPassword) {
                showResult('forgotResult', false, '两次输入的密码不一致');
                return;
            }

            showResult('forgotResult', true, '密码重置成功！请使用新密码登录');
            setTimeout(() => {
                backToLogin();
            }, 2000);
        }

        // 密码强度检查
        function checkPasswordStrength() {
            checkPasswordStrengthForElement('forgotNewPassword', 'forgotPasswordStrength');
        }

        function checkNewPasswordStrength() {
            checkPasswordStrengthForElement('newPassword', 'newPasswordStrength');
        }

        function checkPasswordStrengthForElement(inputId, strengthId) {
            const input = document.getElementById(inputId);
            const strengthElement = document.getElementById(strengthId);
            
            const password = input.value;
            if (!password) {
                strengthElement.textContent = '';
                return;
            }

            try {
                const strength = ApiUtils.checkPasswordStrength(password);
                strengthElement.textContent = strength.message;
                strengthElement.className = `password-strength ${strength.strength}`;
            } catch (error) {
                strengthElement.textContent = '密码强度检查错误';
                strengthElement.className = 'password-strength weak';
            }
        }

        // 密码管理
        function changePassword() {
            const currentPwd = document.getElementById('currentPassword').value;
            const newPwd = document.getElementById('newPassword').value;
            const confirmPwd = document.getElementById('confirmPassword').value;

            if (!currentPwd || !newPwd || !confirmPwd) {
                showResult('passwordResult', false, '请填写完整信息');
                return;
            }

            if (newPwd !== confirmPwd) {
                showResult('passwordResult', false, '两次输入的密码不一致');
                return;
            }

            showResult('passwordResult', true, '密码修改成功！');
            showToast('密码修改成功', 'success');
        }

        // 应用管理
        function createApp() {
            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <h3>创建应用</h3>
                <div class="demo-form">
                    <div class="form-group">
                        <label>应用名称</label>
                        <input type="text" id="appName" placeholder="请输入应用名称">
                    </div>
                    <div class="form-group">
                        <label>应用类型</label>
                        <select id="appType">
                            <option value="1">拍照搜题</option>
                        </select>
                    </div>
                    <button onclick="submitCreateApp()">创建应用</button>
                    <button onclick="hideModal()" class="secondary">取消</button>
                </div>
            `;
            showModal();
        }

        function submitCreateApp() {
            const name = document.getElementById('appName').value;
            if (!name) {
                showToast('请输入应用名称', 'error');
                return;
            }

            hideModal();
            showResult('appsResult', true, '应用创建成功！');
            showToast('应用创建成功', 'success');
        }

        function viewAppDetail(appId) {
            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <h3>应用详情</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 15px;">
                    <p><strong>应用名称:</strong> 我的搜题应用</p>
                    <p><strong>应用类型:</strong> 拍照搜题</p>
                    <p><strong>App Key:</strong> abcd1234efgh5678ijkl9012mnop3456</p>
                    <p><strong>Secret Key:</strong> ********************************</p>
                    <p><strong>状态:</strong> <span style="color: green;">正常</span></p>
                </div>
                <button onclick="hideModal()">关闭</button>
            `;
            showModal();
        }

        function editApp(appId) {
            showToast('编辑应用功能演示', 'info');
        }

        function freezeApp(appId) {
            if (confirm('确定要冻结此应用吗？')) {
                showToast('应用已冻结', 'success');
            }
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            showToast('演示页面加载完成', 'success');
        });
    </script>
</body>
</html>
