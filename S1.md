# 密码管理模块 API 文档

接口地址统一化配置一个config，然后更新下面针对用户登录注册以及用户密码的业务。


## 概述

当前项目实现了用户自主修改密码的功能，通过用户信息更新接口来实现。管理员相关的API接口尚未开发。

### 基础信息

- **Base URL**: `http://localhost:8080`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🔐 已实现功能

### 1. 用户修改密码（使用原密码）

**接口地址**: `PUT /api/v1/user/{user_id}/change-password`

**业务场景**: 用户登录后使用原密码修改密码

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID（路径参数） |
| old_password | string | 是 | 原密码 |
| new_password | string | 是 | 新密码，6-20位字符 |

**密码要求**:
- 长度：6-20个字符
- 包含字母和数字
- 不能包含特殊字符（当前限制）

**请求示例**:
```json
{
  "old_password": "oldpassword123",
  "new_password": "newpassword123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 100.00,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:
- `400`: 密码格式不符合要求
- `401`: 原密码错误
- `403`: 账户已被冻结
- `404`: 用户不存在
- `500`: 服务器内部错误

### 2. 用户忘记密码（发送验证码）

**接口地址**: `POST /api/v1/user/forgot-password`

**业务场景**: 用户忘记密码时发送验证码到手机

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号，11位数字 |

**请求示例**:
```json
{
  "phone": "13800138000"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功"
}
```

**错误响应**:
- `400`: 手机号格式错误
- `403`: 账户已被冻结
- `404`: 手机号未注册
- `429`: 发送过于频繁
- `500`: 服务器内部错误

### 3. 用户重置密码（使用验证码）

**接口地址**: `POST /api/v1/user/reset-password`

**业务场景**: 用户使用验证码重置密码

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号，11位数字 |
| code | string | 是 | 验证码，6位数字 |
| new_password | string | 是 | 新密码，6-20位字符 |

**请求示例**:
```json
{
  "phone": "13800138000",
  "code": "123456",
  "new_password": "newpassword123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 100.00,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:
- `400`: 请求参数错误
- `401`: 验证码错误或已过期
- `403`: 账户已被冻结
- `404`: 手机号未注册
- `500`: 服务器内部错误

### 4. 管理员登录

**接口地址**: `POST /api/v1/admin/login`

**业务场景**: 管理员用户名密码登录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 管理员用户名 |
| password | string | 是 | 管理员密码 |

**请求示例**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "username": "admin",
    "role": 1,
    "role_name": "超级管理员",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:
- `400`: 请求参数错误
- `401`: 用户名或密码错误
- `500`: 服务器内部错误

### 5. 管理员修改密码（使用原密码）

**接口地址**: `PUT /api/v1/admin/{admin_id}/change-password`

**业务场景**: 管理员使用原密码修改密码

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| admin_id | int | 是 | 管理员ID（路径参数） |
| old_password | string | 是 | 原密码 |
| new_password | string | 是 | 新密码，6-20位字符 |

**请求示例**:
```json
{
  "old_password": "oldpassword123",
  "new_password": "newpassword123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": {
    "id": 1,
    "username": "admin",
    "role": 1,
    "role_name": "超级管理员",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:
- `400`: 密码格式不符合要求
- `401`: 原密码错误
- `404`: 管理员不存在
- `500`: 服务器内部错误

### 6. 管理员忘记密码（发送验证码）

**接口地址**: `POST /api/v1/admin/forgot-password`

**业务场景**: 管理员忘记密码时发送验证码

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 管理员用户名 |

**请求示例**:
```json
{
  "username": "admin"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功"
}
```

**错误响应**:
- `400`: 请求参数错误
- `404`: 管理员不存在
- `429`: 发送过于频繁
- `500`: 服务器内部错误

### 7. 管理员重置密码（使用验证码）

**接口地址**: `POST /api/v1/admin/reset-password`

**业务场景**: 管理员使用验证码重置密码

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 管理员用户名 |
| code | string | 是 | 验证码，6位数字 |
| new_password | string | 是 | 新密码，6-20位字符 |

**请求示例**:
```json
{
  "username": "admin",
  "code": "123456",
  "new_password": "newpassword123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "密码重置成功",
  "data": {
    "id": 1,
    "username": "admin",
    "role": 1,
    "role_name": "超级管理员",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:
- `400`: 请求参数错误
- `401`: 验证码错误或已过期
- `404`: 管理员不存在
- `500`: 服务器内部错误

### 8. 管理员重置用户密码

**接口地址**: `PUT /api/v1/admin/{admin_id}/user/{user_id}/reset-password`

**业务场景**: 管理员重置指定用户的密码

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| admin_id | int | 是 | 管理员ID（路径参数） |
| user_id | int | 是 | 用户ID（路径参数） |
| new_password | string | 是 | 新密码，6-20位字符 |

**请求示例**:
```json
{
  "new_password": "newpassword123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "用户密码重置成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 100.00,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:
- `400`: 请求参数错误
- `403`: 权限不足（只有超级管理员可以重置用户密码）
- `404`: 管理员或用户不存在
- `500`: 服务器内部错误

---

## 🔧 技术规范

### 密码要求
- **长度**: 6-20个字符
- **字符类型**: 必须包含字母和数字
- **限制**: 当前不支持特殊字符

### 验证码机制
- **格式**: 6位数字
- **有效期**: 5分钟
- **发送限制**: 60秒内只能发送一次
- **存储**: Redis缓存

### 短信服务
- **服务商**: 阿里云短信服务
- **模板ID**: SMS_294081777
- **签名**: 媒兔记账
- **测试手机号**: 15653259315

### 权限说明
- **用户**: 只能修改自己的密码
- **普通管理员**: 只能修改自己的密码
- **超级管理员**: 可以修改自己的密码和重置用户密码

---

## 📱 前端开发建议

### 1. 密码强度指示器
```javascript
function checkPasswordStrength(password) {
  const hasLetter = /[a-zA-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  const validLength = password.length >= 6 && password.length <= 20;

  if (!validLength) return { valid: false, message: '密码长度必须为6-20位' };
  if (!hasLetter) return { valid: false, message: '密码必须包含字母' };
  if (!hasNumber) return { valid: false, message: '密码必须包含数字' };

  return { valid: true, message: '密码强度良好' };
}
```

### 2. 验证码倒计时
```javascript
function startCountdown(seconds = 60) {
  let count = seconds;
  const timer = setInterval(() => {
    if (count <= 0) {
      clearInterval(timer);
      document.getElementById('sendBtn').disabled = false;
      document.getElementById('sendBtn').textContent = '发送验证码';
    } else {
      document.getElementById('sendBtn').disabled = true;
      document.getElementById('sendBtn').textContent = `${count}秒后重试`;
      count--;
    }
  }, 1000);
}
```

### 3. API调用示例
```javascript
// 用户修改密码
async function changeUserPassword(userId, oldPassword, newPassword) {
  try {
    const response = await fetch(`/api/v1/user/${userId}/change-password`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ old_password: oldPassword, new_password: newPassword })
    });
    const result = await response.json();

    if (result.code === 200) {
      alert('密码修改成功');
    } else {
      alert(result.message);
    }
  } catch (error) {
    alert('网络错误，请稍后重试');
  }
}

// 发送验证码
async function sendVerificationCode(phone) {
  try {
    const response = await fetch('/api/v1/user/forgot-password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phone })
    });
    const result = await response.json();

    if (result.code === 200) {
      startCountdown(60);
      alert('验证码发送成功');
    } else {
      alert(result.message);
    }
  } catch (error) {
    alert('网络错误，请稍后重试');
  }
}
```

### 4. 错误处理
```javascript
function handleApiError(response) {
  switch (response.code) {
    case 400: return '请求参数错误，请检查输入';
    case 401: return '认证失败，请检查密码或验证码';
    case 403: return '权限不足或账户被冻结';
    case 404: return '用户不存在或手机号未注册';
    case 429: return '操作过于频繁，请稍后再试';
    case 500: return '服务器错误，请稍后重试';
    default: return response.message || '未知错误';
  }
}
```

---

## 🧪 测试数据

### 默认管理员账户
- **用户名**: 15688515913
- **密码**: admin888
- **角色**: 超级管理员

### 测试用户
- **手机号**: 15653259315
- **密码**: 123456（初始密码）

### 测试环境
- **服务器地址**: http://localhost:8080
- **数据库**: MySQL (端口3380)
- **缓存**: Redis
- **短信**: 真实阿里云短信服务

---

## 📞 联系方式

如有API相关问题，请联系后端开发团队。

**文档版本**: v1.0
**更新时间**: 2024-06-07
**状态**: ✅ 已完成开发和测试

## 🔄 业务流程

### 1. 用户修改密码流程（使用原密码）

```mermaid
graph TD
    A[用户登录] --> B[进入个人设置页面]
    B --> C[点击修改密码]
    C --> D[输入原密码]
    D --> E[输入新密码]
    E --> F[确认新密码]
    F --> G{前端验证}
    G -->|失败| H[显示错误提示]
    G -->|成功| I[调用修改密码API]
    I --> J{API响应}
    J -->|原密码错误| K[显示原密码错误]
    J -->|成功| L[显示修改成功]
    J -->|其他错误| M[显示错误信息]
    H --> D
    K --> D
    M --> D
    L --> N[建议重新登录]

    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style H fill:#ffcdd2
    style K fill:#ffcdd2
    style M fill:#ffcdd2
```

### 2. 用户忘记密码流程（使用验证码）

```mermaid
graph TD
    A[用户忘记密码] --> B[点击忘记密码]
    B --> C[输入手机号]
    C --> D[点击发送验证码]
    D --> E{手机号验证}
    E -->|未注册| F[显示手机号未注册]
    E -->|已注册| G[发送验证码]
    G --> H[用户收到短信]
    H --> I[输入验证码]
    I --> J[输入新密码]
    J --> K[确认新密码]
    K --> L{前端验证}
    L -->|失败| M[显示错误提示]
    L -->|成功| N[调用重置密码API]
    N --> O{API响应}
    O -->|验证码错误| P[显示验证码错误]
    O -->|成功| Q[显示重置成功]
    O -->|其他错误| R[显示错误信息]
    F --> C
    M --> I
    P --> I
    R --> I
    Q --> S[跳转到登录页面]

    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style F fill:#ffcdd2
    style M fill:#ffcdd2
    style P fill:#ffcdd2
    style R fill:#ffcdd2
```

### 3. 管理员密码管理流程

```mermaid
graph TD
    A[管理员登录] --> B[进入管理后台]
    B --> C{选择操作}
    C -->|修改自己密码| D[输入原密码和新密码]
    C -->|忘记密码| E[输入用户名发送验证码]
    C -->|重置用户密码| F[选择用户输入新密码]

    D --> G[调用管理员修改密码API]
    E --> H[收到验证码]
    H --> I[输入验证码和新密码]
    I --> J[调用管理员重置密码API]
    F --> K[调用重置用户密码API]

    G --> L{响应结果}
    J --> L
    K --> L

    L -->|成功| M[显示操作成功]
    L -->|失败| N[显示错误信息]

    style A fill:#e1f5fe
    style M fill:#c8e6c9
    style N fill:#ffcdd2
```

### 密码修改安全流程

1. **用户身份验证** - 确保用户已登录
2. **权限检查** - 用户只能修改自己的密码
3. **密码验证** - 前端和后端双重验证
4. **密码加密** - 使用bcrypt算法加密存储
5. **操作日志** - 记录密码修改操作

## 💻 前端实现示例

### 1. 修改密码组件

```vue
<template>
  <div class="password-change">
    <h3>修改密码</h3>
    <form @submit.prevent="changePassword">
      <div class="form-group">
        <label>新密码</label>
        <input 
          type="password" 
          v-model="newPassword"
          placeholder="请输入新密码（6-20位字符）"
          :class="{ error: passwordError }"
        />
        <span v-if="passwordError" class="error-text">{{ passwordError }}</span>
      </div>
      
      <div class="form-group">
        <label>确认密码</label>
        <input 
          type="password" 
          v-model="confirmPassword"
          placeholder="请再次输入新密码"
          :class="{ error: confirmError }"
        />
        <span v-if="confirmError" class="error-text">{{ confirmError }}</span>
      </div>
      
      <button type="submit" :disabled="loading">
        {{ loading ? '修改中...' : '修改密码' }}
      </button>
    </form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      newPassword: '',
      confirmPassword: '',
      loading: false,
      passwordError: '',
      confirmError: ''
    }
  },
  
  methods: {
    validatePassword() {
      this.passwordError = '';
      this.confirmError = '';
      
      // 密码长度验证
      if (this.newPassword.length < 6 || this.newPassword.length > 20) {
        this.passwordError = '密码长度必须为6-20个字符';
        return false;
      }
      
      // 密码格式验证
      const passwordRegex = /^[a-zA-Z0-9]+$/;
      if (!passwordRegex.test(this.newPassword)) {
        this.passwordError = '密码只能包含字母和数字';
        return false;
      }
      
      // 确认密码验证
      if (this.newPassword !== this.confirmPassword) {
        this.confirmError = '两次输入的密码不一致';
        return false;
      }
      
      return true;
    },
    
    async changePassword() {
      if (!this.validatePassword()) {
        return;
      }
      
      this.loading = true;
      
      try {
        const response = await fetch(`/api/v1/user/profile/${this.userId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            password: this.newPassword
          })
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
          this.$message.success('密码修改成功');
          this.resetForm();
        } else {
          this.$message.error(result.message || '修改失败');
        }
      } catch (error) {
        this.$message.error('网络错误，请重试');
      } finally {
        this.loading = false;
      }
    },
    
    resetForm() {
      this.newPassword = '';
      this.confirmPassword = '';
      this.passwordError = '';
      this.confirmError = '';
    }
  }
}
</script>
```

### 2. API调用封装

```javascript
// 密码管理API服务
class PasswordAPI {
  constructor(baseURL) {
    this.baseURL = baseURL;
  }

  // 修改用户密码
  async changePassword(userId, newPassword) {
    try {
      const response = await fetch(`${this.baseURL}/api/v1/user/profile/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password: newPassword })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        return { success: true, message: '密码修改成功' };
      } else {
        return { success: false, message: result.message };
      }
    } catch (error) {
      return { success: false, message: '网络错误，请重试' };
    }
  }

  // 验证密码格式
  validatePassword(password) {
    const errors = [];
    
    if (!password) {
      errors.push('密码不能为空');
    } else {
      if (password.length < 6 || password.length > 20) {
        errors.push('密码长度必须为6-20个字符');
      }
      
      if (!/^[a-zA-Z0-9]+$/.test(password)) {
        errors.push('密码只能包含字母和数字');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors: errors
    };
  }
}

// 使用示例
const passwordAPI = new PasswordAPI('http://localhost:8080');

// 修改密码
async function handlePasswordChange(userId, newPassword, confirmPassword) {
  // 1. 前端验证
  const validation = passwordAPI.validatePassword(newPassword);
  if (!validation.valid) {
    alert(validation.errors.join('\n'));
    return;
  }
  
  if (newPassword !== confirmPassword) {
    alert('两次输入的密码不一致');
    return;
  }
  
  // 2. 调用API
  const result = await passwordAPI.changePassword(userId, newPassword);
  
  if (result.success) {
    alert('密码修改成功');
  } else {
    alert(result.message);
  }
}
```

## ⚠️ 待开发功能

### 🚧 管理员功能（未实现）

以下功能需要后续开发：

#### 1. 管理员登录接口
```
POST /api/v1/admin/login
- 管理员用户名密码登录
- JWT Token生成
- 权限验证
```

#### 2. 管理员修改用户密码
```
PUT /api/v1/admin/user/{user_id}/password
- 管理员重置用户密码
- 权限验证（仅超级管理员）
- 操作日志记录
```

#### 3. 找回密码功能
```
POST /api/v1/user/forgot-password
- 发送重置密码验证码
- 验证码验证
- 密码重置
```

#### 4. 管理员密码管理
```
PUT /api/v1/admin/profile/password
- 管理员修改自己的密码
- 旧密码验证
- 新密码设置
```

### 🔧 当前管理员账号

项目中已创建默认管理员账号（仅限命令行管理）：

| 字段 | 值 | 说明 |
|------|-----|------|
| 用户名 | `15688515913` | 管理员登录用户名 |
| 密码 | `admin888` | 当前密码 |
| 角色 | 超级管理员 | 拥有所有权限 |

**命令行管理工具**:
```bash
# 查看管理员列表
go run scripts/manage_admin.go list

# 验证管理员密码
go run scripts/manage_admin.go verify 15688515913 admin888

# 修改管理员密码
go run scripts/manage_admin.go update 15688515913 新密码

# 创建新管理员
go run scripts/manage_admin.go create 用户名 密码 角色
```

## 🔒 安全建议

### 密码安全策略

1. **密码复杂度**:
   - 最小长度6位，最大20位
   - 包含字母和数字
   - 建议后续增加特殊字符支持

2. **密码存储**:
   - 使用bcrypt算法加密
   - 不存储明文密码
   - 密码字段不返回给前端

3. **操作安全**:
   - 用户只能修改自己的密码
   - 修改密码需要用户身份验证
   - 建议增加旧密码验证

### 前端安全建议

1. **输入验证**: 前端进行密码格式验证
2. **敏感信息**: 不在前端存储密码
3. **HTTPS**: 生产环境使用HTTPS传输
4. **会话管理**: 密码修改后建议重新登录

## 📝 注意事项

1. **当前限制**: 只有用户自主修改密码功能
2. **管理员功能**: 需要后续开发完整的管理员API
3. **找回密码**: 暂未实现，需要短信验证码支持
4. **密码策略**: 当前较为简单，建议增强复杂度要求
5. **操作日志**: 建议记录密码修改操作日志



# 密码管理API快速参考 - 前端开发

## 🚀 快速开始

**Base URL**: `http://localhost:8080`  
**Content-Type**: `application/json`

---

## 📋 API接口列表

### 👤 用户密码管理

| 功能 | 方法 | 接口地址 | 说明 |
|------|------|----------|------|
| 修改密码 | PUT | `/api/v1/user/{user_id}/change-password` | 使用原密码修改 |
| 发送验证码 | POST | `/api/v1/user/forgot-password` | 忘记密码发送验证码 |
| 重置密码 | POST | `/api/v1/user/reset-password` | 使用验证码重置密码 |

### 👨‍💼 管理员密码管理

| 功能 | 方法 | 接口地址 | 说明 |
|------|------|----------|------|
| 管理员登录 | POST | `/api/v1/admin/login` | 用户名密码登录 |
| 修改密码 | PUT | `/api/v1/admin/{admin_id}/change-password` | 使用原密码修改 |
| 发送验证码 | POST | `/api/v1/admin/forgot-password` | 忘记密码发送验证码 |
| 重置密码 | POST | `/api/v1/admin/reset-password` | 使用验证码重置密码 |
| 重置用户密码 | PUT | `/api/v1/admin/{admin_id}/user/{user_id}/reset-password` | 管理员重置用户密码 |

---

## 📝 请求参数

### 用户修改密码
```json
PUT /api/v1/user/{user_id}/change-password
{
  "old_password": "oldpass123",
  "new_password": "newpass123"
}
```

### 发送验证码（用户）
```json
POST /api/v1/user/forgot-password
{
  "phone": "13800138000"
}
```

### 重置密码（用户）
```json
POST /api/v1/user/reset-password
{
  "phone": "13800138000",
  "code": "123456",
  "new_password": "newpass123"
}
```

### 管理员登录
```json
POST /api/v1/admin/login
{
  "username": "admin",
  "password": "admin123"
}
```

### 管理员修改密码
```json
PUT /api/v1/admin/{admin_id}/change-password
{
  "old_password": "oldpass123",
  "new_password": "newpass123"
}
```

### 发送验证码（管理员）
```json
POST /api/v1/admin/forgot-password
{
  "username": "admin"
}
```

### 重置密码（管理员）
```json
POST /api/v1/admin/reset-password
{
  "username": "admin",
  "code": "123456",
  "new_password": "newpass123"
}
```

### 管理员重置用户密码
```json
PUT /api/v1/admin/{admin_id}/user/{user_id}/reset-password
{
  "new_password": "newpass123"
}
```

---

## ✅ 成功响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 用户或管理员信息
  }
}
```

---

## ❌ 错误码说明

| 错误码 | 说明 | 常见原因 |
|--------|------|----------|
| 400 | 请求参数错误 | 参数格式不正确、必填参数缺失 |
| 401 | 认证失败 | 密码错误、验证码错误或过期 |
| 403 | 权限不足 | 账户被冻结、权限不够 |
| 404 | 资源不存在 | 用户不存在、手机号未注册 |
| 429 | 请求过于频繁 | 验证码发送间隔不足60秒 |
| 500 | 服务器错误 | 系统内部错误 |

---

## 🔧 技术要求

### 密码规则
- **长度**: 6-20位
- **字符**: 必须包含字母和数字
- **限制**: 不支持特殊字符

### 验证码规则
- **格式**: 6位数字
- **有效期**: 5分钟
- **发送限制**: 60秒内只能发送一次

### 权限说明
- **用户**: 只能修改自己的密码
- **普通管理员**: 只能修改自己的密码
- **超级管理员**: 可以重置用户密码

---

## 🧪 测试数据

### 默认管理员
- **用户名**: `15688515913`
- **密码**: `admin888`
- **角色**: 超级管理员

### 测试用户
- **手机号**: `15653259315`
- **密码**: `123456`

---

## 💻 JavaScript示例

### 基础API调用
```javascript
// 通用API调用函数
async function callAPI(url, method, data = null) {
  try {
    const options = {
      method,
      headers: { 'Content-Type': 'application/json' }
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(url, options);
    const result = await response.json();
    
    return result;
  } catch (error) {
    throw new Error('网络错误');
  }
}

// 用户修改密码
async function changeUserPassword(userId, oldPassword, newPassword) {
  return await callAPI(
    `/api/v1/user/${userId}/change-password`,
    'PUT',
    { old_password: oldPassword, new_password: newPassword }
  );
}

// 发送验证码
async function sendVerificationCode(phone) {
  return await callAPI(
    '/api/v1/user/forgot-password',
    'POST',
    { phone }
  );
}

// 重置密码
async function resetPassword(phone, code, newPassword) {
  return await callAPI(
    '/api/v1/user/reset-password',
    'POST',
    { phone, code, new_password: newPassword }
  );
}

// 管理员登录
async function adminLogin(username, password) {
  return await callAPI(
    '/api/v1/admin/login',
    'POST',
    { username, password }
  );
}
```

### 验证码倒计时
```javascript
function startCountdown(buttonId, seconds = 60) {
  const button = document.getElementById(buttonId);
  let count = seconds;
  
  button.disabled = true;
  
  const timer = setInterval(() => {
    if (count <= 0) {
      clearInterval(timer);
      button.disabled = false;
      button.textContent = '发送验证码';
    } else {
      button.textContent = `${count}秒后重试`;
      count--;
    }
  }, 1000);
}
```

### 密码强度验证
```javascript
function validatePassword(password) {
  const errors = [];
  
  if (password.length < 6 || password.length > 20) {
    errors.push('密码长度必须为6-20位');
  }
  
  if (!/[a-zA-Z]/.test(password)) {
    errors.push('密码必须包含字母');
  }
  
  if (!/\d/.test(password)) {
    errors.push('密码必须包含数字');
  }
  
  return {
    valid: errors.length === 0,
    errors: errors
  };
}
```

### 错误处理
```javascript
function handleError(result) {
  const errorMessages = {
    400: '请求参数错误，请检查输入',
    401: '认证失败，请检查密码或验证码',
    403: '权限不足或账户被冻结',
    404: '用户不存在或手机号未注册',
    429: '操作过于频繁，请稍后再试',
    500: '服务器错误，请稍后重试'
  };
  
  return errorMessages[result.code] || result.message || '未知错误';
}
```

---

## 📞 支持

如有问题请联系后端开发团队。

**文档版本**: v1.0  
**更新时间**: 2024-06-07
