# 应用管理模块 - 前端开发快速指南

## 🚀 快速开始

### 基础信息
- **API Base URL**: `http://localhost:8080`
- **模块功能**: 用户应用创建和管理
- **核心概念**: 每个应用有唯一的 `app_key` 和 `secret_key`

## 📋 核心业务规则

| 规则 | 说明 |
|------|------|
| 应用数量限制 | 每用户最多5个应用 |
| 密钥唯一性 | app_key全局唯一（32位） |
| 密钥安全性 | secret_key可重置（64位） |
| 权限隔离 | 用户只能操作自己的应用 |
| 状态控制 | 冻结应用无法调用API |

## 🎯 主要页面设计

### 1. 应用列表页面
```
┌─────────────────────────────────────┐
│ 我的应用                [+ 创建应用] │
├─────────────────────────────────────┤
│ 📱 我的搜题应用          [正常]     │
│    App Key: abcd1234...             │
│    创建时间: 2024-01-01             │
│    [详情] [编辑] [冻结]             │
├─────────────────────────────────────┤
│ 📱 测试应用              [冻结]     │
│    App Key: efgh5678...             │
│    创建时间: 2024-01-02             │
│    [详情] [编辑] [恢复]             │
└─────────────────────────────────────┘
```

### 2. 应用详情页面
```
┌─────────────────────────────────────┐
│ 应用详情                    [编辑]  │
├─────────────────────────────────────┤
│ 应用名称: 我的搜题应用              │
│ 应用类型: 拍照搜题                  │
│ 应用状态: [正常] [切换为冻结]       │
├─────────────────────────────────────┤
│ App Key:                            │
│ abcd1234efgh5678ijkl9012mnop3456    │
│                            [复制]   │
├─────────────────────────────────────┤
│ Secret Key:                         │
│ ********************************    │
│ [显示] [复制] [重置密钥]            │
├─────────────────────────────────────┤
│ 创建时间: 2024-01-01 12:00:00       │
│ 更新时间: 2024-01-01 12:00:00       │
└─────────────────────────────────────┘
```

### 3. 创建应用页面
```
┌─────────────────────────────────────┐
│ 创建应用                            │
├─────────────────────────────────────┤
│ 应用名称: [________________]        │
│          (1-50个字符)               │
├─────────────────────────────────────┤
│ 业务类型: [拍照搜题 ▼]              │
├─────────────────────────────────────┤
│           [取消] [创建应用]         │
└─────────────────────────────────────┘
```

## 🔌 API 接口速查

### 基础CRUD操作

| 操作 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 创建 | POST | `/api/v1/user/{user_id}/app` | 创建新应用 |
| 列表 | GET | `/api/v1/user/{user_id}/app` | 获取应用列表 |
| 详情 | GET | `/api/v1/user/{user_id}/app/{app_id}` | 获取应用详情 |
| 更新 | PUT | `/api/v1/user/{user_id}/app/{app_id}` | 更新应用名称 |

### 特殊操作

| 操作 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 重置密钥 | PUT | `/api/v1/user/{user_id}/app/{app_id}/reset-secret` | 重置SecretKey |
| 状态管理 | PUT | `/api/v1/user/{user_id}/app/{app_id}/status` | 冻结/恢复应用 |

## 💻 代码示例

### 1. 获取应用列表
```javascript
async function getApplications(userId) {
  try {
    const response = await fetch(`/api/v1/user/${userId}/app`);
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data; // 应用列表数组
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取应用列表失败:', error);
    throw error;
  }
}
```

### 2. 创建应用
```javascript
async function createApplication(userId, name, type = 1) {
  try {
    const response = await fetch(`/api/v1/user/${userId}/app`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name, type })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 创建成功，返回包含密钥的完整信息
      return result.data;
    } else if (result.code === 409) {
      throw new Error('应用数量已达上限（5个）');
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建应用失败:', error);
    throw error;
  }
}
```

### 3. 重置密钥
```javascript
async function resetSecretKey(userId, appId) {
  // 先确认用户操作
  if (!confirm('重置密钥后，旧密钥将立即失效。确定要继续吗？')) {
    return;
  }
  
  try {
    const response = await fetch(`/api/v1/user/${userId}/app/${appId}/reset-secret`, {
      method: 'PUT'
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 返回包含新密钥的应用信息
      alert('密钥重置成功！请及时更新客户端配置。');
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('重置密钥失败:', error);
    throw error;
  }
}
```

## 🎨 UI/UX 建议

### 状态显示
```css
/* 应用状态样式 */
.status-normal {
  background: #52c41a;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-frozen {
  background: #ff4d4f;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
}
```

### 密钥显示
- 默认隐藏SecretKey，显示为星号
- 提供"显示/隐藏"切换按钮
- 提供一键复制功能
- 重置密钥需要二次确认

### 交互反馈
- 创建成功后显示密钥信息弹窗
- 操作按钮添加loading状态
- 错误信息友好提示
- 成功操作给予明确反馈

## ⚠️ 重要提醒

### 安全注意事项
1. **密钥保护**: SecretKey只在创建和重置时显示，请提醒用户妥善保存
2. **操作确认**: 重置密钥、冻结应用等操作需要用户确认
3. **权限验证**: 确保用户只能操作自己的应用

### 错误处理
```javascript
// 统一错误处理
function handleApiError(error, result) {
  switch (result?.code) {
    case 400:
      return '请求参数错误，请检查输入信息';
    case 403:
      return '账户已被冻结，请联系管理员';
    case 404:
      return '应用不存在或已被删除';
    case 409:
      return '应用数量已达上限（5个），请删除不需要的应用';
    case 500:
      return '服务器错误，请稍后重试';
    default:
      return result?.message || '操作失败，请重试';
  }
}
```

### 数据验证
```javascript
// 前端验证规则
const validation = {
  appName: {
    required: true,
    minLength: 1,
    maxLength: 50,
    message: '应用名称为1-50个字符'
  },
  appType: {
    required: true,
    enum: [1],
    message: '请选择有效的应用类型'
  }
};
```

## 📱 响应式设计

### 移动端适配
- 应用卡片采用垂直布局
- 操作按钮适当增大点击区域
- 密钥显示考虑横向滚动
- 确认对话框适配小屏幕

### 桌面端优化
- 应用列表采用表格或卡片网格布局
- 支持批量操作
- 提供搜索和筛选功能
- 密钥显示支持快速复制

## 🔄 状态管理建议

```javascript
// Vue 3 + Pinia 示例
export const useApplicationStore = defineStore('application', {
  state: () => ({
    applications: [],
    loading: false,
    error: null
  }),
  
  actions: {
    async fetchApplications(userId) {
      this.loading = true;
      try {
        this.applications = await getApplications(userId);
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    }
  }
});
```

这个快速指南为前端开发团队提供了完整的应用管理模块开发指导，包括UI设计、API调用、错误处理和最佳实践建议。


# 应用管理模块 API 文档

## 概述

应用管理模块为用户提供创建和管理应用的功能。每个应用都有唯一的 `app_key` 和 `secret_key`，用于后续的API调用认证。

### 基础信息

- **Base URL**: `http://localhost:8080`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8

### 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

## 业务流程

### 1. 应用管理流程图

```
用户登录 → 查看应用列表 → 创建新应用 → 获取密钥 → 使用API服务
    ↓
管理应用 → 修改名称/重置密钥/冻结应用
```

### 2. 核心业务规则

1. **应用数量限制**: 每个用户最多可创建 5 个应用
2. **密钥唯一性**: `app_key` 全局唯一，32位随机字符串
3. **密钥安全**: `secret_key` 为64位随机字符串，可重置
4. **权限隔离**: 用户只能操作自己创建的应用
5. **状态控制**: 应用可以被冻结，冻结后无法调用相关API
6. **类型限制**: 应用类型创建后不可修改

### 3. 应用状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | 正常 | 应用可正常使用 |
| 2 | 冻结 | 应用被冻结，无法调用API |

### 4. 应用类型说明

| 类型值 | 类型名称 | 说明 |
|--------|----------|------|
| 1 | 拍照搜题 | 支持图片识别和题目搜索 |

## API 接口详情

### 1. 创建应用

**接口地址**: `POST /api/v1/user/{user_id}/app`

**业务场景**: 用户需要创建新的应用来获取API调用凭证

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID（路径参数） |
| name | string | 是 | 应用名称，1-50个字符 |
| type | int | 是 | 业务类型，当前只支持1（拍照搜题） |

**请求示例**:
```json
{
  "name": "我的搜题应用",
  "type": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "应用创建成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12",
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:
- `409`: 应用数量超限（已创建5个应用）
- `403`: 账户已被冻结

### 2. 获取应用列表

**接口地址**: `GET /api/v1/user/{user_id}/app`

**业务场景**: 用户查看自己创建的所有应用

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID（路径参数） |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取应用列表成功",
  "data": [
    {
      "id": 1,
      "name": "我的搜题应用",
      "type": 1,
      "app_key": "abcd1234efgh5678ijkl9012mnop3456",
      "status": 1,
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ]
}
```

**注意**: 列表接口不返回 `secret_key`，需要查看详情获取

### 3. 获取应用详情

**接口地址**: `GET /api/v1/user/{user_id}/app/{app_id}`

**业务场景**: 用户查看应用的完整信息，包括密钥

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID（路径参数） |
| app_id | int | 是 | 应用ID（路径参数） |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取应用详情成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12",
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 4. 更新应用信息

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}`

**业务场景**: 用户修改应用名称

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID（路径参数） |
| app_id | int | 是 | 应用ID（路径参数） |
| name | string | 是 | 新的应用名称，1-50个字符 |

**请求示例**:
```json
{
  "name": "新的应用名称"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "应用更新成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "新的应用名称",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12",
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**注意**: 应用类型（type）创建后不可修改

### 5. 重置SecretKey

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}/reset-secret`

**业务场景**: 密钥泄露或定期更换时重置SecretKey

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID（路径参数） |
| app_id | int | 是 | 应用ID（路径参数） |

**响应示例**:
```json
{
  "code": 200,
  "message": "SecretKey重置成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "newabcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuv",
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**重要提醒**: 重置后旧的SecretKey立即失效，需要更新客户端配置

### 6. 更新应用状态

**接口地址**: `PUT /api/v1/user/{user_id}/app/{app_id}/status`

**业务场景**: 临时冻结或恢复应用

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID（路径参数） |
| app_id | int | 是 | 应用ID（路径参数） |
| status | int | 是 | 应用状态，1-正常，2-冻结 |

**请求示例**:
```json
{
  "status": 2
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "应用状态更新成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "name": "我的搜题应用",
    "type": 1,
    "app_key": "abcd1234efgh5678ijkl9012mnop3456",
    "secret_key": "abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12",
    "status": 2,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

## 前端开发指南

### 1. 页面设计建议

#### 应用列表页面
- 显示应用名称、类型、状态、创建时间
- 提供"创建应用"按钮
- 每个应用提供"查看详情"、"编辑"、"冻结/恢复"操作

#### 应用详情页面
- 显示完整的应用信息
- 提供复制app_key和secret_key的功能
- 提供"重置密钥"按钮（需要确认）
- 提供"编辑名称"功能

#### 创建应用页面
- 应用名称输入框（必填，1-50字符）
- 应用类型选择（当前只有"拍照搜题"）
- 创建成功后显示生成的密钥

### 2. 交互流程建议

1. **首次使用流程**:
   ```
   用户登录 → 应用列表（空） → 点击创建应用 → 填写信息 → 创建成功 → 显示密钥
   ```

2. **日常管理流程**:
   ```
   应用列表 → 选择应用 → 查看详情 → 执行操作（编辑/重置密钥/状态管理）
   ```

### 3. 错误处理建议

- **409错误**: 提示"应用数量已达上限（5个），请删除不需要的应用后重试"
- **403错误**: 提示"账户已被冻结，请联系管理员"
- **404错误**: 提示"应用不存在或已被删除"

### 4. 安全提醒

- 在显示SecretKey时添加安全提醒
- 重置密钥前需要用户确认
- 建议定期更换密钥
- 不要在前端代码中硬编码密钥

### 5. 状态显示建议

- 正常状态：绿色标识
- 冻结状态：红色标识，禁用相关操作
- 提供状态切换的快捷操作

## 测试用例

### 功能测试
1. 创建应用（正常情况）
2. 创建应用（达到数量限制）
3. 获取应用列表
4. 查看应用详情
5. 修改应用名称
6. 重置SecretKey
7. 冻结/恢复应用

### 边界测试
1. 应用名称长度验证
2. 无效的应用类型
3. 访问其他用户的应用
4. 冻结用户的操作限制

## 业务流程详细说明

### 1. 完整业务流程图

```mermaid
graph TD
    A[用户登录] --> B[进入应用管理页面]
    B --> C{是否有应用?}
    C -->|否| D[显示空状态页面]
    C -->|是| E[显示应用列表]

    D --> F[点击创建应用]
    E --> F
    E --> G[查看应用详情]
    E --> H[编辑应用]
    E --> I[管理应用状态]

    F --> J[填写应用信息]
    J --> K{验证信息}
    K -->|失败| L[显示错误提示]
    K -->|成功| M[调用创建API]
    M --> N{创建结果}
    N -->|失败| O[显示错误信息]
    N -->|成功| P[显示密钥信息]
    P --> Q[返回应用列表]

    G --> R[显示完整信息]
    R --> S[复制密钥]
    R --> T[重置密钥]
    R --> U[编辑名称]

    H --> V[修改应用名称]
    V --> W[保存修改]

    I --> X[冻结/恢复应用]
    X --> Y[确认操作]
    Y --> Z[更新状态]

    L --> J
    O --> B
    Q --> E
    S --> R
    T --> AA[确认重置]
    AA --> BB[生成新密钥]
    BB --> R
    U --> V
    W --> E
    Z --> E
```

### 2. 关键业务场景

#### 场景1: 新用户首次创建应用
```
1. 用户登录系统
2. 进入应用管理页面（显示空状态）
3. 点击"创建应用"按钮
4. 填写应用名称，选择业务类型
5. 提交创建请求
6. 系统生成app_key和secret_key
7. 显示创建成功页面，展示密钥信息
8. 用户保存密钥信息
9. 返回应用列表页面
```

#### 场景2: 密钥泄露处理
```
1. 用户发现密钥可能泄露
2. 进入应用详情页面
3. 点击"重置密钥"按钮
4. 系统弹出确认对话框
5. 用户确认重置操作
6. 系统生成新的secret_key
7. 显示新密钥信息
8. 用户更新客户端配置
```

#### 场景3: 应用状态管理
```
1. 用户需要临时停用应用
2. 在应用列表中找到目标应用
3. 点击"冻结"操作
4. 系统弹出确认对话框
5. 用户确认冻结操作
6. 应用状态更新为"冻结"
7. 该应用的API调用被禁用
```

## 前端组件设计建议

### 1. 组件结构
```
ApplicationManagement/
├── ApplicationList.vue          # 应用列表页面
├── ApplicationDetail.vue        # 应用详情页面
├── CreateApplication.vue        # 创建应用页面
├── EditApplication.vue          # 编辑应用页面
└── components/
    ├── ApplicationCard.vue      # 应用卡片组件
    ├── KeyDisplay.vue          # 密钥显示组件
    ├── StatusBadge.vue         # 状态标识组件
    └── ConfirmDialog.vue       # 确认对话框组件
```

### 2. 状态管理建议
```javascript
// Vuex/Pinia store
const applicationStore = {
  state: {
    applications: [],
    currentApplication: null,
    loading: false,
    error: null
  },
  actions: {
    async fetchApplications(userId) { },
    async createApplication(userId, data) { },
    async updateApplication(userId, appId, data) { },
    async resetSecretKey(userId, appId) { },
    async updateStatus(userId, appId, status) { }
  }
}
```

### 3. 关键交互设计

#### 密钥显示组件
```vue
<template>
  <div class="key-display">
    <label>{{ label }}</label>
    <div class="key-value">
      <span v-if="!showKey">{{ maskedKey }}</span>
      <span v-else>{{ value }}</span>
      <button @click="toggleShow">{{ showKey ? '隐藏' : '显示' }}</button>
      <button @click="copyToClipboard">复制</button>
    </div>
  </div>
</template>
```

#### 状态切换组件
```vue
<template>
  <div class="status-control">
    <StatusBadge :status="application.status" />
    <button
      @click="toggleStatus"
      :class="statusButtonClass"
    >
      {{ statusButtonText }}
    </button>
  </div>
</template>
```

## API调用示例

### JavaScript/Vue.js示例

```javascript
// API服务封装
class ApplicationAPI {
  constructor(baseURL) {
    this.baseURL = baseURL;
  }

  // 创建应用
  async createApplication(userId, data) {
    const response = await fetch(`${this.baseURL}/api/v1/user/${userId}/app`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });
    return response.json();
  }

  // 获取应用列表
  async getApplications(userId) {
    const response = await fetch(`${this.baseURL}/api/v1/user/${userId}/app`);
    return response.json();
  }

  // 获取应用详情
  async getApplicationDetail(userId, appId) {
    const response = await fetch(`${this.baseURL}/api/v1/user/${userId}/app/${appId}`);
    return response.json();
  }

  // 重置密钥
  async resetSecretKey(userId, appId) {
    const response = await fetch(`${this.baseURL}/api/v1/user/${userId}/app/${appId}/reset-secret`, {
      method: 'PUT'
    });
    return response.json();
  }
}

// 使用示例
const api = new ApplicationAPI('http://localhost:8080');

// 创建应用
try {
  const result = await api.createApplication(1, {
    name: '我的搜题应用',
    type: 1
  });

  if (result.code === 200) {
    console.log('应用创建成功:', result.data);
    // 保存密钥信息
    localStorage.setItem('app_key', result.data.app_key);
    localStorage.setItem('secret_key', result.data.secret_key);
  } else {
    console.error('创建失败:', result.message);
  }
} catch (error) {
  console.error('网络错误:', error);
}
```

## 注意事项

1. **密钥安全**: SecretKey只在创建和重置时返回，请妥善保存
2. **数量限制**: 每个用户最多5个应用，需要在前端做相应提示
3. **权限控制**: 用户只能操作自己的应用
4. **状态影响**: 冻结的应用无法调用后续的API服务
5. **类型限制**: 应用类型创建后不可修改
6. **密钥格式**: app_key为32位，secret_key为64位随机字符串
7. **状态同步**: 应用状态变更后需要刷新列表显示
8. **错误处理**: 建议统一处理API错误响应
