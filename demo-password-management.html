<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码管理功能演示</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        .demo-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .demo-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .demo-sidebar {
            background: #667eea;
            color: white;
            padding: 20px;
        }
        .demo-main {
            padding: 20px;
        }
        .demo-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .demo-nav li {
            margin-bottom: 10px;
        }
        .demo-nav a {
            color: white;
            text-decoration: none;
            padding: 10px;
            display: block;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .demo-nav a:hover,
        .demo-nav a.active {
            background: rgba(255, 255, 255, 0.2);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .feature-card h4 {
            color: #333;
            margin-bottom: 10px;
        }
        .feature-card p {
            color: #666;
            margin-bottom: 15px;
        }
        .api-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .api-item {
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 5px;
            padding: 5px;
            background: white;
            border-radius: 3px;
        }
        .step-indicator {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🔐 密码管理模块演示</h1>
            <p>基于S1.md文档实现的完整密码管理功能展示</p>
        </div>

        <div class="demo-content">
            <div style="display: grid; grid-template-columns: 250px 1fr;">
                <div class="demo-sidebar">
                    <h3>📋 功能导航</h3>
                    <ul class="demo-nav">
                        <li><a href="#overview" class="active">功能概览</a></li>
                        <li><a href="#user-password" onclick="showUserPasswordDemo()">用户密码管理</a></li>
                        <li><a href="#forgot-password" onclick="showForgotPasswordDemo()">忘记密码</a></li>
                        <li><a href="#admin-features" onclick="showAdminFeaturesDemo()">管理员功能</a></li>
                        <li><a href="#security" onclick="showSecurityDemo()">安全特性</a></li>
                    </ul>
                </div>

                <div class="demo-main">
                    <div id="overview" class="demo-section">
                        <h2>🔐 密码管理功能概览</h2>
                        
                        <div class="step-indicator">
                            <strong>💡 提示：</strong>点击左侧导航可以体验不同的功能模块
                        </div>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <h4>👤 用户密码管理</h4>
                                <p>用户可以使用原密码修改新密码，支持密码强度验证</p>
                                <div class="api-list">
                                    <div class="api-item">PUT /api/v1/user/{user_id}/change-password</div>
                                </div>
                                <button class="btn btn-primary" onclick="showChangePasswordModal()">体验修改密码</button>
                            </div>

                            <div class="feature-card">
                                <h4>📱 忘记密码</h4>
                                <p>通过手机验证码重置密码，支持验证码倒计时</p>
                                <div class="api-list">
                                    <div class="api-item">POST /api/v1/user/forgot-password</div>
                                    <div class="api-item">POST /api/v1/user/reset-password</div>
                                </div>
                                <button class="btn btn-primary" onclick="showForgotPasswordDemo()">体验忘记密码</button>
                            </div>

                            <div class="feature-card">
                                <h4>👨‍💼 管理员登录</h4>
                                <p>管理员用户名密码登录，支持角色权限管理</p>
                                <div class="api-list">
                                    <div class="api-item">POST /api/v1/admin/login</div>
                                </div>
                                <button class="btn btn-primary" onclick="testAdminLogin()">体验管理员登录</button>
                            </div>

                            <div class="feature-card">
                                <h4>🔧 管理员密码管理</h4>
                                <p>管理员可以修改自己的密码和重置用户密码</p>
                                <div class="api-list">
                                    <div class="api-item">PUT /api/v1/admin/{admin_id}/change-password</div>
                                    <div class="api-item">PUT /api/v1/admin/{admin_id}/user/{user_id}/reset-password</div>
                                </div>
                                <button class="btn btn-primary" onclick="showAdminResetPasswordModal(1, '测试用户')">体验重置用户密码</button>
                            </div>

                            <div class="feature-card">
                                <h4>🛡️ 密码安全</h4>
                                <p>密码强度验证、格式检查、安全提示</p>
                                <div class="api-list">
                                    <div class="api-item">密码长度：6-20位</div>
                                    <div class="api-item">必须包含：字母和数字</div>
                                </div>
                                <button class="btn btn-primary" onclick="showPasswordValidationDemo()">体验密码验证</button>
                            </div>

                            <div class="feature-card">
                                <h4>📞 验证码机制</h4>
                                <p>短信验证码发送、倒计时、有效期管理</p>
                                <div class="api-list">
                                    <div class="api-item">格式：6位数字</div>
                                    <div class="api-item">有效期：5分钟</div>
                                    <div class="api-item">发送限制：60秒</div>
                                </div>
                                <button class="btn btn-primary" onclick="testSendCode()">体验验证码</button>
                            </div>
                        </div>

                        <div class="step-indicator">
                            <strong>📋 技术规范：</strong>
                            <ul style="margin: 10px 0 0 20px;">
                                <li>密码长度：6-20个字符</li>
                                <li>字符类型：必须包含字母和数字</li>
                                <li>验证码：6位数字，5分钟有效期</li>
                                <li>发送限制：60秒内只能发送一次</li>
                                <li>权限控制：用户只能修改自己的密码</li>
                            </ul>
                        </div>
                    </div>

                    <div id="demoContent" style="margin-top: 30px;">
                        <!-- 演示内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal()">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast"></div>

    <script src="api.js"></script>
    <script>
        // 模拟应用状态
        const appState = {
            currentUser: { id: 1, phone: '13800138000' },
            isLoggedIn: true,
            isAdmin: false
        };

        // Toast管理器
        const toast = {
            success: (msg) => showToast(msg, 'success'),
            error: (msg) => showToast(msg, 'error'),
            warning: (msg) => showToast(msg, 'warning'),
            info: (msg) => showToast(msg, 'info')
        };

        function showToast(message, type) {
            const toastEl = document.getElementById('toast');
            toastEl.textContent = message;
            toastEl.className = `toast ${type} show`;
            setTimeout(() => {
                toastEl.classList.remove('show');
            }, 3000);
        }

        // 模态框管理
        function showModal() {
            document.getElementById('modal').style.display = 'block';
        }

        function hideModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 演示函数
        function showUserPasswordDemo() {
            updateActiveNav('user-password');
            showChangePasswordModal();
        }

        function showForgotPasswordDemo() {
            updateActiveNav('forgot-password');
            const content = document.getElementById('demoContent');
            content.innerHTML = `
                <h3>📱 忘记密码流程演示</h3>
                <div class="step-indicator">
                    <strong>流程说明：</strong>
                    <ol style="margin: 10px 0 0 20px;">
                        <li>用户输入手机号</li>
                        <li>点击发送验证码（60秒倒计时）</li>
                        <li>输入收到的6位验证码</li>
                        <li>设置新密码并确认</li>
                        <li>提交重置密码</li>
                    </ol>
                </div>
                <button class="btn btn-primary" onclick="testSendCode()">模拟发送验证码</button>
            `;
        }

        function showAdminFeaturesDemo() {
            updateActiveNav('admin-features');
            const content = document.getElementById('demoContent');
            content.innerHTML = `
                <h3>👨‍💼 管理员功能演示</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>管理员登录</h4>
                        <p>默认管理员账户：15688515913 / admin888</p>
                        <button class="btn btn-primary" onclick="testAdminLogin()">测试登录</button>
                    </div>
                    <div class="feature-card">
                        <h4>重置用户密码</h4>
                        <p>超级管理员可以重置任意用户的密码</p>
                        <button class="btn btn-danger" onclick="showAdminResetPasswordModal(1, '测试用户')">重置密码</button>
                    </div>
                </div>
            `;
        }

        function showSecurityDemo() {
            updateActiveNav('security');
            const content = document.getElementById('demoContent');
            content.innerHTML = `
                <h3>🛡️ 安全特性演示</h3>
                <div class="password-form">
                    <div class="form-group">
                        <label>测试密码强度</label>
                        <input type="password" id="demoPassword" placeholder="输入密码查看强度" oninput="testPasswordStrength()">
                        <div class="password-strength" id="demoPasswordStrength"></div>
                    </div>
                </div>
            `;
        }

        function showPasswordValidationDemo() {
            showSecurityDemo();
        }

        function testSendCode() {
            toast.info('验证码发送功能演示 - 实际需要后端API支持');
            startCountdownDemo();
        }

        function testAdminLogin() {
            toast.info('管理员登录功能演示 - 实际需要后端API支持');
        }

        function startCountdownDemo() {
            let count = 60;
            toast.info(`验证码倒计时演示：${count}秒`);
            const timer = setInterval(() => {
                count--;
                if (count <= 0) {
                    clearInterval(timer);
                    toast.success('倒计时结束，可以重新发送');
                } else if (count % 10 === 0) {
                    toast.info(`倒计时：${count}秒`);
                }
            }, 1000);
        }

        function testPasswordStrength() {
            const input = document.getElementById('demoPassword');
            const strengthElement = document.getElementById('demoPasswordStrength');
            
            if (!input || !strengthElement) return;

            const password = input.value;
            if (!password) {
                strengthElement.textContent = '';
                strengthElement.className = 'password-strength';
                return;
            }

            const strength = ApiUtils.checkPasswordStrength(password);
            strengthElement.textContent = strength.message;
            strengthElement.className = `password-strength ${strength.strength}`;
        }

        function updateActiveNav(section) {
            document.querySelectorAll('.demo-nav a').forEach(a => a.classList.remove('active'));
            document.querySelector(`[href="#${section}"]`)?.classList.add('active');
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
    <script src="script.js"></script>
</body>
</html>
