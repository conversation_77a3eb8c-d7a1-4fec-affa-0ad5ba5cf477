<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终测试版本</title>
    <style>
        /* 页面切换 */
        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        /* 登录页面样式 - 修复后的版本 */
        #loginPage.active, #registerPage.active {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }

        .login-footer {
            text-align: center;
            margin-top: 20px;
        }

        .login-footer a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
        }

        .debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 200px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div class="debug" id="debug">
        等待初始化...
    </div>

    <!-- 登录页面 -->
    <div id="loginPage" class="page active">
        <div class="login-container">
            <div class="login-header">
                <h1>拍照搜题管理系统</h1>
                <p>请登录您的账户</p>
            </div>
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="phone">手机号</label>
                    <input type="tel" id="phone" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                </div>
                <button type="submit" class="login-btn">登录</button>
            </form>
            <div class="login-footer">
                <a href="#" id="showRegister">没有账户？立即注册</a>
            </div>
        </div>
    </div>

    <!-- 注册页面 -->
    <div id="registerPage" class="page">
        <div class="login-container">
            <div class="login-header">
                <h1>用户注册</h1>
                <p>创建您的账户</p>
            </div>
            <form id="registerForm" class="login-form">
                <div class="form-group">
                    <label for="regPhone">手机号</label>
                    <input type="tel" id="regPhone" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <div class="form-group">
                    <label for="regPassword">密码</label>
                    <input type="password" id="regPassword" name="password" placeholder="6-20位字符" required>
                </div>
                <div class="form-group">
                    <label for="verifyCode">验证码</label>
                    <input type="text" id="verifyCode" name="code" placeholder="请输入验证码" required>
                </div>
                <div class="form-group">
                    <label for="inviteCode">邀请码</label>
                    <input type="text" id="inviteCode" name="invite_code" placeholder="请输入邀请码" value="SOLVE2024" required>
                </div>
                <button type="submit" class="login-btn">注册</button>
            </form>
            <div class="login-footer">
                <a href="#" id="showLogin">已有账户？立即登录</a>
            </div>
        </div>
    </div>

    <script>
        // 调试函数
        function debug(message) {
            console.log(message);
            const debugEl = document.getElementById('debug');
            if (debugEl) {
                debugEl.innerHTML = new Date().toLocaleTimeString() + '<br>' + message;
            }
        }

        // 页面切换函数
        function showPage(pageId) {
            debug('切换到页面: ' + pageId);
            
            const pages = ['loginPage', 'registerPage'];
            pages.forEach(id => {
                const page = document.getElementById(id);
                if (page) {
                    const isActive = id === pageId;
                    page.classList.toggle('active', isActive);
                    debug('页面 ' + id + ': ' + (isActive ? '显示' : '隐藏'));
                } else {
                    debug('错误: 未找到页面 ' + id);
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            debug('DOM加载完成');

            // 绑定页面切换事件
            const showRegisterLink = document.getElementById('showRegister');
            const showLoginLink = document.getElementById('showLogin');

            debug('showRegister: ' + (showRegisterLink ? '找到' : '未找到'));
            debug('showLogin: ' + (showLoginLink ? '找到' : '未找到'));

            if (showRegisterLink) {
                showRegisterLink.addEventListener('click', function(e) {
                    debug('点击注册链接');
                    e.preventDefault();
                    showPage('registerPage');
                });
            }

            if (showLoginLink) {
                showLoginLink.addEventListener('click', function(e) {
                    debug('点击登录链接');
                    e.preventDefault();
                    showPage('loginPage');
                });
            }

            debug('初始化完成');
        });
    </script>
</body>
</html>
