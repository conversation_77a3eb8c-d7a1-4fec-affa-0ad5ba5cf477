// API 配置
const API_CONFIG = {
    baseURL: 'http://localhost:8080',
    version: 'v1',
    timeout: 10000
};

// API 工具类
class ApiClient {
    constructor() {
        this.baseURL = `${API_CONFIG.baseURL}/api/${API_CONFIG.version}`;
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (data.code === 200) {
                return { success: true, data: data.data, message: data.message };
            } else {
                return { success: false, error: data.message, code: data.code };
            }
        } catch (error) {
            console.error('API请求错误:', error);
            return { success: false, error: '网络请求失败', code: 500 };
        }
    }

    // GET 请求
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url);
    }

    // POST 请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT 请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE 请求
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
}

// API 服务类
class ApiService {
    constructor() {
        this.client = new ApiClient();
    }

    // 用户注册
    async register(userData) {
        return this.client.post('/user/register', userData);
    }

    // 用户登录
    async login(credentials) {
        return this.client.post('/user/login', credentials);
    }

    // 获取用户信息
    async getUserProfile(userId) {
        return this.client.get(`/user/profile/${userId}`);
    }

    // 更新用户信息
    async updateUserProfile(userId, userData) {
        return this.client.put(`/user/profile/${userId}`, userData);
    }

    // 发送验证码
    async sendVerificationCode(phone) {
        return this.client.post('/user/send-code', { phone });
    }

    // 健康检查
    async healthCheck() {
        try {
            const response = await fetch(`${API_CONFIG.baseURL}/health`);
            return await response.json();
        } catch (error) {
            console.error('健康检查失败:', error);
            return { status: 'error', message: '服务不可用' };
        }
    }

    // 获取用户列表（管理员功能）
    async getUserList(page = 1, limit = 10) {
        // 注意：这个接口在当前文档中没有定义，这里是预留的管理员功能
        return this.client.get('/admin/users', { page, limit });
    }

    // 删除用户（管理员功能）
    async deleteUser(userId) {
        // 注意：这个接口在当前文档中没有定义，这里是预留的管理员功能
        return this.client.delete(`/admin/users/${userId}`);
    }

    // 冻结/解冻用户（管理员功能）
    async toggleUserStatus(userId, status) {
        // 注意：这个接口在当前文档中没有定义，这里是预留的管理员功能
        return this.client.put(`/admin/users/${userId}/status`, { status });
    }

    // ========== 应用管理 API ==========

    // 创建应用
    async createApplication(userId, data) {
        return this.client.post(`/user/${userId}/app`, data);
    }

    // 获取应用列表
    async getApplications(userId) {
        return this.client.get(`/user/${userId}/app`);
    }

    // 获取应用详情
    async getApplicationDetail(userId, appId) {
        return this.client.get(`/user/${userId}/app/${appId}`);
    }

    // 更新应用信息
    async updateApplication(userId, appId, data) {
        return this.client.put(`/user/${userId}/app/${appId}`, data);
    }

    // 重置SecretKey
    async resetSecretKey(userId, appId) {
        return this.client.put(`/user/${userId}/app/${appId}/reset-secret`);
    }

    // 更新应用状态
    async updateApplicationStatus(userId, appId, status) {
        return this.client.put(`/user/${userId}/app/${appId}/status`, { status });
    }

    // ========== 密码管理 API ==========

    // 用户修改密码（使用原密码）
    async changeUserPassword(userId, oldPassword, newPassword) {
        return this.client.put(`/user/${userId}/change-password`, {
            old_password: oldPassword,
            new_password: newPassword
        });
    }

    // 用户忘记密码（发送验证码）
    async sendForgotPasswordCode(phone) {
        return this.client.post('/user/forgot-password', { phone });
    }

    // 用户重置密码（使用验证码）
    async resetUserPassword(phone, code, newPassword) {
        return this.client.post('/user/reset-password', {
            phone,
            code,
            new_password: newPassword
        });
    }

    // 管理员登录
    async adminLogin(username, password) {
        return this.client.post('/admin/login', { username, password });
    }

    // 管理员修改密码
    async changeAdminPassword(adminId, oldPassword, newPassword) {
        return this.client.put(`/admin/${adminId}/change-password`, {
            old_password: oldPassword,
            new_password: newPassword
        });
    }

    // 管理员忘记密码（发送验证码）
    async sendAdminForgotPasswordCode(username) {
        return this.client.post('/admin/forgot-password', { username });
    }

    // 管理员重置密码（使用验证码）
    async resetAdminPassword(username, code, newPassword) {
        return this.client.post('/admin/reset-password', {
            username,
            code,
            new_password: newPassword
        });
    }

    // 管理员重置用户密码
    async adminResetUserPassword(adminId, userId, newPassword) {
        return this.client.put(`/admin/${adminId}/user/${userId}/reset-password`, {
            new_password: newPassword
        });
    }
}

// 创建全局 API 服务实例
const apiService = new ApiService();

// 工具函数
const ApiUtils = {
    // 验证手机号格式
    validatePhone(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    },

    // 验证密码格式
    validatePassword(password) {
        return password && password.length >= 6 && password.length <= 20;
    },

    // 验证验证码格式
    validateCode(code) {
        const codeRegex = /^\d{6}$/;
        return codeRegex.test(code);
    },

    // 格式化时间
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },

    // 格式化用户状态
    formatUserStatus(status) {
        const statusMap = {
            1: { text: '正常', class: 'status-normal' },
            2: { text: '冻结', class: 'status-frozen' }
        };
        return statusMap[status] || { text: '未知', class: 'status-unknown' };
    },

    // 格式化余额
    formatBalance(balance) {
        if (typeof balance !== 'number') return '0.00';
        return balance.toFixed(2);
    },

    // 错误处理
    handleApiError(error, defaultMessage = '操作失败') {
        if (typeof error === 'string') {
            return error;
        }
        
        if (error && error.message) {
            return error.message;
        }
        
        return defaultMessage;
    },

    // 成功处理
    handleApiSuccess(response, defaultMessage = '操作成功') {
        if (response && response.message) {
            return response.message;
        }

        return defaultMessage;
    },

    // 格式化应用状态
    formatApplicationStatus(status) {
        const statusMap = {
            1: { text: '正常', class: 'status-normal' },
            2: { text: '冻结', class: 'status-frozen' }
        };
        return statusMap[status] || { text: '未知', class: 'status-unknown' };
    },

    // 格式化应用类型
    formatApplicationType(type) {
        const typeMap = {
            1: '拍照搜题'
        };
        return typeMap[type] || '未知类型';
    },

    // 验证应用名称
    validateApplicationName(name) {
        return name && name.trim().length >= 1 && name.trim().length <= 50;
    },

    // 格式化密钥显示（部分隐藏）
    formatKeyDisplay(key, showLength = 8) {
        if (!key) return '';
        if (key.length <= showLength * 2) return key;
        return key.substring(0, showLength) + '...' + key.substring(key.length - showLength);
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
                return true;
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                const result = document.execCommand('copy');
                document.body.removeChild(textArea);
                return result;
            }
        } catch (error) {
            console.error('复制失败:', error);
            return false;
        }
    },

    // ========== 密码管理工具函数 ==========

    // 验证密码格式
    validatePassword(password) {
        const errors = [];

        if (!password) {
            errors.push('密码不能为空');
            return { valid: false, errors };
        }

        // 长度验证
        if (password.length < 6 || password.length > 20) {
            errors.push('密码长度必须为6-20个字符');
        }

        // 字符类型验证
        const hasLetter = /[a-zA-Z]/.test(password);
        const hasNumber = /\d/.test(password);

        if (!hasLetter) {
            errors.push('密码必须包含字母');
        }

        if (!hasNumber) {
            errors.push('密码必须包含数字');
        }

        // 字符限制验证
        const validChars = /^[a-zA-Z0-9]+$/.test(password);
        if (!validChars) {
            errors.push('密码只能包含字母和数字');
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    },

    // 验证手机号格式
    validatePhoneNumber(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    },

    // 验证验证码格式
    validateVerificationCode(code) {
        const codeRegex = /^\d{6}$/;
        return codeRegex.test(code);
    },

    // 验证用户名格式（管理员）
    validateUsername(username) {
        return username && username.trim().length >= 3 && username.trim().length <= 20;
    },

    // 密码强度检查
    checkPasswordStrength(password) {
        const validation = this.validatePassword(password);
        if (!validation.valid) {
            return { strength: 'weak', message: validation.errors[0] };
        }

        let score = 0;

        // 长度加分
        if (password.length >= 8) score += 1;
        if (password.length >= 12) score += 1;

        // 字符类型加分
        if (/[a-z]/.test(password)) score += 1;
        if (/[A-Z]/.test(password)) score += 1;
        if (/\d/.test(password)) score += 1;

        if (score <= 2) {
            return { strength: 'weak', message: '密码强度较弱' };
        } else if (score <= 4) {
            return { strength: 'medium', message: '密码强度中等' };
        } else {
            return { strength: 'strong', message: '密码强度良好' };
        }
    },

    // 处理密码管理API错误
    handlePasswordError(result) {
        const errorMessages = {
            400: '请求参数错误，请检查输入信息',
            401: '认证失败，请检查密码或验证码',
            403: '权限不足或账户被冻结',
            404: '用户不存在或手机号未注册',
            429: '操作过于频繁，请稍后再试',
            500: '服务器错误，请稍后重试'
        };

        return errorMessages[result.code] || result.message || '操作失败，请重试';
    }
};

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApiService, ApiUtils, apiService };
}
