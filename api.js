// API 配置
const API_CONFIG = {
    baseURL: 'http://localhost:8080',
    version: 'v1',
    timeout: 10000
};

// API 工具类
class ApiClient {
    constructor() {
        this.baseURL = `${API_CONFIG.baseURL}/api/${API_CONFIG.version}`;
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (data.code === 200) {
                return { success: true, data: data.data, message: data.message };
            } else {
                return { success: false, error: data.message, code: data.code };
            }
        } catch (error) {
            console.error('API请求错误:', error);
            return { success: false, error: '网络请求失败', code: 500 };
        }
    }

    // GET 请求
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url);
    }

    // POST 请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT 请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE 请求
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
}

// API 服务类
class ApiService {
    constructor() {
        this.client = new ApiClient();
    }

    // 用户注册
    async register(userData) {
        return this.client.post('/user/register', userData);
    }

    // 用户登录
    async login(credentials) {
        return this.client.post('/user/login', credentials);
    }

    // 获取用户信息
    async getUserProfile(userId) {
        return this.client.get(`/user/profile/${userId}`);
    }

    // 更新用户信息
    async updateUserProfile(userId, userData) {
        return this.client.put(`/user/profile/${userId}`, userData);
    }

    // 发送验证码
    async sendVerificationCode(phone) {
        return this.client.post('/user/send-code', { phone });
    }

    // 健康检查
    async healthCheck() {
        try {
            const response = await fetch(`${API_CONFIG.baseURL}/health`);
            return await response.json();
        } catch (error) {
            console.error('健康检查失败:', error);
            return { status: 'error', message: '服务不可用' };
        }
    }

    // 获取用户列表（管理员功能）
    async getUserList(page = 1, limit = 10) {
        // 注意：这个接口在当前文档中没有定义，这里是预留的管理员功能
        return this.client.get('/admin/users', { page, limit });
    }

    // 删除用户（管理员功能）
    async deleteUser(userId) {
        // 注意：这个接口在当前文档中没有定义，这里是预留的管理员功能
        return this.client.delete(`/admin/users/${userId}`);
    }

    // 冻结/解冻用户（管理员功能）
    async toggleUserStatus(userId, status) {
        // 注意：这个接口在当前文档中没有定义，这里是预留的管理员功能
        return this.client.put(`/admin/users/${userId}/status`, { status });
    }
}

// 创建全局 API 服务实例
const apiService = new ApiService();

// 工具函数
const ApiUtils = {
    // 验证手机号格式
    validatePhone(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    },

    // 验证密码格式
    validatePassword(password) {
        return password && password.length >= 6 && password.length <= 20;
    },

    // 验证验证码格式
    validateCode(code) {
        const codeRegex = /^\d{6}$/;
        return codeRegex.test(code);
    },

    // 格式化时间
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },

    // 格式化用户状态
    formatUserStatus(status) {
        const statusMap = {
            1: { text: '正常', class: 'status-normal' },
            2: { text: '冻结', class: 'status-frozen' }
        };
        return statusMap[status] || { text: '未知', class: 'status-unknown' };
    },

    // 格式化余额
    formatBalance(balance) {
        if (typeof balance !== 'number') return '0.00';
        return balance.toFixed(2);
    },

    // 错误处理
    handleApiError(error, defaultMessage = '操作失败') {
        if (typeof error === 'string') {
            return error;
        }
        
        if (error && error.message) {
            return error.message;
        }
        
        return defaultMessage;
    },

    // 成功处理
    handleApiSuccess(response, defaultMessage = '操作成功') {
        if (response && response.message) {
            return response.message;
        }
        
        return defaultMessage;
    }
};

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApiService, ApiUtils, apiService };
}
