<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态演示 - 拍照搜题管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .btn {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status-display {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .status-history {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .status-item {
            padding: 8px;
            margin-bottom: 8px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .status-item.success {
            border-left-color: #28a745;
        }
        .status-item.error {
            border-left-color: #dc3545;
        }
        .status-time {
            font-size: 12px;
            color: #666;
        }
        .status-message {
            font-weight: bold;
            margin: 5px 0;
        }
        .status-details {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>登录注册状态演示</h1>
        <p>这个页面演示了登录、注册和发送验证码功能的详细状态返回。</p>

        <!-- 登录演示 -->
        <div class="demo-section">
            <h3>🔐 登录功能演示</h3>
            <form id="demoLoginForm">
                <div class="form-group">
                    <label for="demoPhone">手机号</label>
                    <input type="tel" id="demoPhone" placeholder="输入13800138000测试成功，其他测试失败" value="13800138000">
                </div>
                <div class="form-group">
                    <label for="demoPassword">密码</label>
                    <input type="password" id="demoPassword" placeholder="输入123456测试成功，其他测试失败" value="123456">
                </div>
                <button type="submit" class="btn">登录测试</button>
                <button type="button" class="btn" onclick="testLoginError()">测试登录失败</button>
            </form>
            <div id="loginStatus" class="status-display" style="display: none;"></div>
        </div>

        <!-- 注册演示 -->
        <div class="demo-section">
            <h3>📝 注册功能演示</h3>
            <form id="demoRegisterForm">
                <div class="form-group">
                    <label for="demoRegPhone">手机号</label>
                    <input type="tel" id="demoRegPhone" placeholder="输入新手机号测试成功" value="13900139000">
                </div>
                <div class="form-group">
                    <label for="demoRegPassword">密码</label>
                    <input type="password" id="demoRegPassword" placeholder="输入密码" value="123456">
                </div>
                <div class="form-group">
                    <label for="demoCode">验证码</label>
                    <input type="text" id="demoCode" placeholder="输入123456测试成功" value="123456">
                </div>
                <div class="form-group">
                    <label for="demoInviteCode">邀请码</label>
                    <input type="text" id="demoInviteCode" value="SOLVE2024">
                </div>
                <button type="submit" class="btn">注册测试</button>
                <button type="button" class="btn" onclick="testRegisterError()">测试注册失败</button>
            </form>
            <div id="registerStatus" class="status-display" style="display: none;"></div>
        </div>

        <!-- 验证码演示 -->
        <div class="demo-section">
            <h3>📱 发送验证码演示</h3>
            <div class="form-group">
                <label for="demoCodePhone">手机号</label>
                <input type="tel" id="demoCodePhone" placeholder="输入手机号" value="13900139000">
            </div>
            <button type="button" class="btn" id="demoSendCodeBtn" onclick="testSendCode()">发送验证码</button>
            <button type="button" class="btn" onclick="testSendCodeError()">测试发送失败</button>
            <div id="sendCodeStatus" class="status-display" style="display: none;"></div>
        </div>

        <!-- 状态历史 -->
        <div class="demo-section">
            <h3>📊 操作状态历史</h3>
            <button type="button" class="btn" onclick="showStatusHistory()">刷新状态历史</button>
            <button type="button" class="btn" onclick="clearStatusHistory()">清空历史</button>
            <div id="statusHistory" class="status-history"></div>
        </div>
    </div>

    <!-- 引入原有的脚本文件 -->
    <script src="api.js"></script>
    <script>
        // 简化的状态管理器（用于演示）
        class DemoStatusManager {
            constructor() {
                this.statusHistory = [];
            }

            recordStatus(operation, result) {
                const status = {
                    timestamp: new Date().toISOString(),
                    operation: operation,
                    success: result.success,
                    message: result.message || result.error,
                    type: result.type,
                    code: result.code,
                    details: result.details
                };

                this.statusHistory.unshift(status);
                console.log(`[${operation}] ${result.success ? '成功' : '失败'}:`, result);
                return status;
            }

            getStatusHistory() {
                return [...this.statusHistory];
            }

            clearHistory() {
                this.statusHistory = [];
            }
        }

        const demoStatusManager = new DemoStatusManager();

        // 模拟登录功能
        async function demoLogin(loginData) {
            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (loginData.phone === '13800138000' && loginData.password === '123456') {
                return {
                    success: true,
                    data: { id: 1, phone: loginData.phone, balance: 100 },
                    message: '登录成功',
                    type: 'login_success'
                };
            } else {
                return {
                    success: false,
                    error: '手机号或密码错误',
                    code: 401,
                    type: 'api_error'
                };
            }
        }

        // 模拟注册功能
        async function demoRegister(registerData) {
            await new Promise(resolve => setTimeout(resolve, 1500));

            if (registerData.phone === '13800138000') {
                return {
                    success: false,
                    error: '手机号已注册',
                    code: 409,
                    type: 'api_error'
                };
            } else if (registerData.code === '123456') {
                return {
                    success: true,
                    data: { id: 2, phone: registerData.phone, balance: 0 },
                    message: '注册成功',
                    type: 'register_success'
                };
            } else {
                return {
                    success: false,
                    error: '验证码错误',
                    code: 400,
                    type: 'api_error'
                };
            }
        }

        // 模拟发送验证码
        async function demoSendCode(phone) {
            await new Promise(resolve => setTimeout(resolve, 800));

            if (phone.length === 11) {
                return {
                    success: true,
                    message: '验证码发送成功',
                    type: 'send_code_success'
                };
            } else {
                return {
                    success: false,
                    error: '手机号格式错误',
                    code: 400,
                    type: 'api_error'
                };
            }
        }

        // 显示状态
        function showStatus(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `status-display ${result.success ? 'status-success' : 'status-error'}`;
            
            const icon = result.success ? '✅' : '❌';
            const statusText = result.success ? '成功' : '失败';
            
            element.innerHTML = `
                <div><strong>${icon} ${statusText}</strong></div>
                <div>消息: ${result.message || result.error}</div>
                <div>类型: ${result.type}</div>
                ${result.code ? `<div>状态码: ${result.code}</div>` : ''}
                ${result.details ? `<div>详情: ${result.details}</div>` : ''}
                <div>时间: ${new Date().toLocaleString()}</div>
            `;
        }

        // 登录测试
        document.getElementById('demoLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const phone = document.getElementById('demoPhone').value;
            const password = document.getElementById('demoPassword').value;
            
            const btn = e.target.querySelector('button[type="submit"]');
            btn.disabled = true;
            btn.textContent = '登录中...';
            
            const result = await demoLogin({ phone, password });
            demoStatusManager.recordStatus('用户登录', result);
            showStatus('loginStatus', result);
            
            btn.disabled = false;
            btn.textContent = '登录测试';
        });

        // 注册测试
        document.getElementById('demoRegisterForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const phone = document.getElementById('demoRegPhone').value;
            const password = document.getElementById('demoRegPassword').value;
            const code = document.getElementById('demoCode').value;
            const invite_code = document.getElementById('demoInviteCode').value;
            
            const btn = e.target.querySelector('button[type="submit"]');
            btn.disabled = true;
            btn.textContent = '注册中...';
            
            const result = await demoRegister({ phone, password, code, invite_code });
            demoStatusManager.recordStatus('用户注册', result);
            showStatus('registerStatus', result);
            
            btn.disabled = false;
            btn.textContent = '注册测试';
        });

        // 发送验证码测试
        async function testSendCode() {
            const phone = document.getElementById('demoCodePhone').value;
            const btn = document.getElementById('demoSendCodeBtn');
            
            btn.disabled = true;
            btn.textContent = '发送中...';
            
            const result = await demoSendCode(phone);
            demoStatusManager.recordStatus('发送验证码', result);
            showStatus('sendCodeStatus', result);
            
            btn.disabled = false;
            btn.textContent = '发送验证码';
        }

        // 测试错误情况
        function testLoginError() {
            const result = {
                success: false,
                error: '网络连接失败',
                type: 'network_error',
                details: 'Connection timeout'
            };
            demoStatusManager.recordStatus('用户登录', result);
            showStatus('loginStatus', result);
        }

        function testRegisterError() {
            const result = {
                success: false,
                error: '服务器内部错误',
                code: 500,
                type: 'api_error'
            };
            demoStatusManager.recordStatus('用户注册', result);
            showStatus('registerStatus', result);
        }

        function testSendCodeError() {
            const result = {
                success: false,
                error: '发送过于频繁',
                code: 429,
                type: 'api_error'
            };
            demoStatusManager.recordStatus('发送验证码', result);
            showStatus('sendCodeStatus', result);
        }

        // 显示状态历史
        function showStatusHistory() {
            const history = demoStatusManager.getStatusHistory();
            const container = document.getElementById('statusHistory');
            
            if (history.length === 0) {
                container.innerHTML = '<p>暂无操作历史</p>';
                return;
            }
            
            container.innerHTML = history.map(status => {
                const icon = status.success ? '✅' : '❌';
                const time = new Date(status.timestamp).toLocaleString();
                return `
                    <div class="status-item ${status.success ? 'success' : 'error'}">
                        <div class="status-time">${time}</div>
                        <div class="status-message">${icon} ${status.operation}: ${status.message}</div>
                        <div class="status-details">类型: ${status.type} ${status.code ? `| 状态码: ${status.code}` : ''}</div>
                    </div>
                `;
            }).join('');
        }

        // 清空状态历史
        function clearStatusHistory() {
            demoStatusManager.clearHistory();
            document.getElementById('statusHistory').innerHTML = '<p>历史已清空</p>';
        }

        // 页面加载时显示初始状态
        document.addEventListener('DOMContentLoaded', function() {
            showStatusHistory();
        });
    </script>
</body>
</html>
