<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化测试版本</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .page {
            display: none;
            min-height: 100vh;
            padding: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .page.active {
            display: block;
        }
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .register-page {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .link {
            color: #667eea;
            text-decoration: none;
            cursor: pointer;
        }
        .link:hover {
            text-decoration: underline;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
        }
        .debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="debug" id="debug">
        调试信息将显示在这里
    </div>

    <!-- 登录页面 -->
    <div id="loginPage" class="page login-page active">
        <div class="container">
            <h1>登录</h1>
            <form id="loginForm">
                <div class="form-group">
                    <label for="phone">手机号</label>
                    <input type="tel" id="phone" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                </div>
                <button type="submit" class="btn">登录</button>
            </form>
            <div class="footer">
                <a href="#" class="link" id="showRegister">没有账户？立即注册</a>
            </div>
        </div>
    </div>

    <!-- 注册页面 -->
    <div id="registerPage" class="page register-page">
        <div class="container">
            <h1>注册</h1>
            <form id="registerForm">
                <div class="form-group">
                    <label for="regPhone">手机号</label>
                    <input type="tel" id="regPhone" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <div class="form-group">
                    <label for="regPassword">密码</label>
                    <input type="password" id="regPassword" name="password" placeholder="6-20位字符" required>
                </div>
                <div class="form-group">
                    <label for="verifyCode">验证码</label>
                    <input type="text" id="verifyCode" name="code" placeholder="请输入验证码" required>
                </div>
                <div class="form-group">
                    <label for="inviteCode">邀请码</label>
                    <input type="text" id="inviteCode" name="invite_code" placeholder="请输入邀请码" value="SOLVE2024" required>
                </div>
                <button type="submit" class="btn">注册</button>
            </form>
            <div class="footer">
                <a href="#" class="link" id="showLogin">已有账户？立即登录</a>
            </div>
        </div>
    </div>

    <script>
        // 调试函数
        function debug(message) {
            console.log(message);
            const debugEl = document.getElementById('debug');
            if (debugEl) {
                debugEl.innerHTML = new Date().toLocaleTimeString() + ': ' + message;
            }
        }

        // 页面切换函数
        function showPage(pageId) {
            debug('切换到页面: ' + pageId);
            
            const pages = ['loginPage', 'registerPage'];
            pages.forEach(id => {
                const page = document.getElementById(id);
                if (page) {
                    const isActive = id === pageId;
                    page.classList.toggle('active', isActive);
                    debug('页面 ' + id + ': ' + (isActive ? '显示' : '隐藏'));
                } else {
                    debug('错误: 未找到页面 ' + id);
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            debug('DOM加载完成');

            // 绑定页面切换事件
            const showRegisterLink = document.getElementById('showRegister');
            const showLoginLink = document.getElementById('showLogin');

            debug('showRegister元素: ' + (showRegisterLink ? '找到' : '未找到'));
            debug('showLogin元素: ' + (showLoginLink ? '找到' : '未找到'));

            if (showRegisterLink) {
                showRegisterLink.addEventListener('click', function(e) {
                    debug('点击注册链接');
                    e.preventDefault();
                    showPage('registerPage');
                });
            }

            if (showLoginLink) {
                showLoginLink.addEventListener('click', function(e) {
                    debug('点击登录链接');
                    e.preventDefault();
                    showPage('loginPage');
                });
            }

            // 绑定表单提交事件
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    debug('登录表单提交');
                    alert('登录功能（模拟）');
                });
            }

            const registerForm = document.getElementById('registerForm');
            if (registerForm) {
                registerForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    debug('注册表单提交');
                    alert('注册功能（模拟）');
                });
            }

            debug('初始化完成');
        });
    </script>
</body>
</html>
