<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面切换</title>
    <style>
        .page {
            display: none;
            padding: 20px;
            border: 2px solid #ccc;
            margin: 10px;
        }
        .page.active {
            display: block;
        }
        .login-page {
            background: #f0f8ff;
        }
        .register-page {
            background: #f0fff0;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>页面切换测试</h1>
    
    <div>
        <button onclick="showPage('loginPage')">显示登录页</button>
        <button onclick="showPage('registerPage')">显示注册页</button>
    </div>

    <!-- 登录页面 -->
    <div id="loginPage" class="page login-page active">
        <h2>登录页面</h2>
        <p>这是登录页面内容</p>
        <a href="#" onclick="showPage('registerPage'); return false;">没有账户？立即注册</a>
    </div>

    <!-- 注册页面 -->
    <div id="registerPage" class="page register-page">
        <h2>注册页面</h2>
        <p>这是注册页面内容</p>
        <a href="#" onclick="showPage('loginPage'); return false;">已有账户？立即登录</a>
    </div>

    <script>
        function showPage(pageId) {
            console.log('切换到页面:', pageId);
            
            // 隐藏所有页面
            const pages = ['loginPage', 'registerPage'];
            pages.forEach(id => {
                const page = document.getElementById(id);
                if (page) {
                    page.classList.remove('active');
                    console.log('隐藏页面:', id);
                }
            });
            
            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                console.log('显示页面:', pageId);
            } else {
                console.error('未找到页面:', pageId);
            }
        }

        // 测试DOM加载
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
            
            // 测试元素是否存在
            const loginPage = document.getElementById('loginPage');
            const registerPage = document.getElementById('registerPage');
            
            console.log('loginPage元素:', loginPage);
            console.log('registerPage元素:', registerPage);
        });
    </script>
</body>
</html>
