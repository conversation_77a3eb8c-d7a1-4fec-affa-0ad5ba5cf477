<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用管理功能测试</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .mock-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>应用管理功能测试</h1>
        
        <div class="test-section">
            <h3>1. 应用列表展示测试</h3>
            <p>测试应用卡片的渲染和样式</p>
            <button class="btn btn-primary" onclick="testApplicationsList()">加载测试数据</button>
            
            <div id="testAppsList" class="apps-list" style="margin-top: 20px;">
                <!-- 测试应用卡片将在这里显示 -->
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. 创建应用模态框测试</h3>
            <p>测试创建应用的表单和交互</p>
            <button class="btn btn-primary" onclick="showCreateApplicationModal()">打开创建应用模态框</button>
        </div>
        
        <div class="test-section">
            <h3>3. 应用详情模态框测试</h3>
            <p>测试应用详情的显示和密钥管理</p>
            <button class="btn btn-primary" onclick="testApplicationDetail()">显示应用详情</button>
        </div>
        
        <div class="test-section">
            <h3>4. 密钥复制功能测试</h3>
            <p>测试密钥复制到剪贴板功能</p>
            <button class="btn btn-primary" onclick="testCopyFunction()">测试复制功能</button>
        </div>
        
        <div class="test-section">
            <h3>5. 空状态展示测试</h3>
            <p>测试无应用时的空状态显示</p>
            <button class="btn btn-primary" onclick="testEmptyState()">显示空状态</button>
            
            <div id="testEmptyState" style="margin-top: 20px; display: none;">
                <div class="empty-state">
                    <div class="empty-icon">📱</div>
                    <h3>暂无应用</h3>
                    <p>您还没有创建任何应用，点击上方"创建应用"按钮开始创建您的第一个应用</p>
                    <button class="btn btn-primary" onclick="showCreateApplicationModal()">创建应用</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal()">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast"></div>

    <script src="api.js"></script>
    <script>
        // 模拟应用状态
        const appState = {
            currentUser: { id: 1, phone: '13800138000' },
            isLoggedIn: true
        };

        const applicationState = {
            applications: [],
            currentApplication: null,
            loading: false
        };

        // 模拟Toast管理器
        const toast = {
            success: (msg) => showToast(msg, 'success'),
            error: (msg) => showToast(msg, 'error'),
            warning: (msg) => showToast(msg, 'warning'),
            info: (msg) => showToast(msg, 'info')
        };

        function showToast(message, type) {
            const toastEl = document.getElementById('toast');
            toastEl.textContent = message;
            toastEl.className = `toast ${type} show`;
            setTimeout(() => {
                toastEl.classList.remove('show');
            }, 3000);
        }

        // 模态框管理
        function showModal() {
            document.getElementById('modal').style.display = 'block';
        }

        function hideModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 测试函数
        function testApplicationsList() {
            const mockApps = [
                {
                    id: 1,
                    name: '我的搜题应用',
                    type: 1,
                    app_key: 'abcd1234efgh5678ijkl9012mnop3456',
                    status: 1,
                    created_at: '2024-01-01T12:00:00Z',
                    updated_at: '2024-01-01T12:00:00Z'
                },
                {
                    id: 2,
                    name: '测试应用',
                    type: 1,
                    app_key: 'efgh5678ijkl9012mnop3456abcd1234',
                    status: 2,
                    created_at: '2024-01-02T10:30:00Z',
                    updated_at: '2024-01-02T10:30:00Z'
                }
            ];

            applicationState.applications = mockApps;
            const container = document.getElementById('testAppsList');
            container.innerHTML = mockApps.map(app => createApplicationCard(app)).join('');
        }

        function testApplicationDetail() {
            const mockApp = {
                id: 1,
                name: '我的搜题应用',
                type: 1,
                app_key: 'abcd1234efgh5678ijkl9012mnop3456',
                secret_key: 'abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12',
                status: 1,
                created_at: '2024-01-01T12:00:00Z',
                updated_at: '2024-01-01T12:00:00Z'
            };

            applicationState.currentApplication = mockApp;
            showApplicationDetailModal(mockApp);
        }

        function testCopyFunction() {
            copyKey('test-key-12345', 'Test Key');
        }

        function testEmptyState() {
            const element = document.getElementById('testEmptyState');
            element.style.display = element.style.display === 'none' ? 'block' : 'none';
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
    <script src="script.js"></script>
</body>
</html>
