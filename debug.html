<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试版本</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .page {
            display: none;
            padding: 20px;
            border: 2px solid #ccc;
            margin: 10px 0;
            border-radius: 5px;
        }
        .page.active {
            display: block;
        }
        #loginPage {
            background: #e3f2fd;
        }
        #registerPage {
            background: #e8f5e8;
        }
        .btn {
            padding: 10px 20px;
            margin: 10px 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
        .link {
            color: #007bff;
            text-decoration: none;
            cursor: pointer;
        }
        .link:hover {
            text-decoration: underline;
        }
        .debug {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>拍照搜题管理系统 - 调试版本</h1>
    
    <div class="debug">
        <h3>调试控制</h3>
        <button class="btn" onclick="showPage('loginPage')">显示登录页</button>
        <button class="btn" onclick="showPage('registerPage')">显示注册页</button>
        <button class="btn" onclick="testElements()">测试元素</button>
        <button class="btn" onclick="console.clear()">清空控制台</button>
    </div>

    <!-- 登录页面 -->
    <div id="loginPage" class="page active">
        <h2>登录页面</h2>
        <form id="loginForm">
            <div>
                <label>手机号:</label>
                <input type="tel" id="phone" placeholder="请输入11位手机号" required>
            </div>
            <br>
            <div>
                <label>密码:</label>
                <input type="password" id="password" placeholder="请输入密码" required>
            </div>
            <br>
            <button type="submit" class="btn">登录</button>
        </form>
        <br>
        <a href="#" class="link" onclick="showPage('registerPage'); return false;">没有账户？立即注册</a>
    </div>

    <!-- 注册页面 -->
    <div id="registerPage" class="page">
        <h2>注册页面</h2>
        <form id="registerForm">
            <div>
                <label>手机号:</label>
                <input type="tel" id="regPhone" placeholder="请输入11位手机号" required>
            </div>
            <br>
            <div>
                <label>密码:</label>
                <input type="password" id="regPassword" placeholder="6-20位字符" required>
            </div>
            <br>
            <div>
                <label>验证码:</label>
                <input type="text" id="verifyCode" placeholder="请输入验证码" required>
                <button type="button" class="btn" onclick="sendCode()">发送验证码</button>
            </div>
            <br>
            <div>
                <label>邀请码:</label>
                <input type="text" id="inviteCode" value="SOLVE2024" required>
            </div>
            <br>
            <button type="submit" class="btn">注册</button>
        </form>
        <br>
        <a href="#" class="link" onclick="showPage('loginPage'); return false;">已有账户？立即登录</a>
    </div>

    <div class="debug">
        <h3>调试信息</h3>
        <div id="debugInfo">等待初始化...</div>
    </div>

    <script>
        // 调试信息显示
        function log(message) {
            console.log(message);
            const debugInfo = document.getElementById('debugInfo');
            if (debugInfo) {
                debugInfo.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
            }
        }

        // 页面切换函数
        function showPage(pageId) {
            log('切换到页面: ' + pageId);
            
            const pages = ['loginPage', 'registerPage'];
            pages.forEach(id => {
                const page = document.getElementById(id);
                if (page) {
                    const isActive = id === pageId;
                    page.classList.toggle('active', isActive);
                    log('页面 ' + id + ': ' + (isActive ? '显示' : '隐藏'));
                } else {
                    log('错误: 未找到页面 ' + id);
                }
            });
        }

        // 测试元素是否存在
        function testElements() {
            log('=== 测试元素 ===');
            const elements = ['loginPage', 'registerPage', 'loginForm', 'registerForm'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                log('元素 ' + id + ': ' + (element ? '存在' : '不存在'));
            });
        }

        // 发送验证码
        function sendCode() {
            const phone = document.getElementById('regPhone').value;
            if (!phone) {
                alert('请先输入手机号');
                return;
            }
            log('发送验证码到: ' + phone);
            alert('验证码发送成功（模拟）');
        }

        // 表单提交处理
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM加载完成');
            
            // 登录表单
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const phone = document.getElementById('phone').value;
                    const password = document.getElementById('password').value;
                    log('登录尝试: ' + phone);
                    alert('登录功能（模拟）: ' + phone);
                });
                log('登录表单事件已绑定');
            }

            // 注册表单
            const registerForm = document.getElementById('registerForm');
            if (registerForm) {
                registerForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const phone = document.getElementById('regPhone').value;
                    log('注册尝试: ' + phone);
                    alert('注册功能（模拟）: ' + phone);
                });
                log('注册表单事件已绑定');
            }

            testElements();
            log('初始化完成');
        });
    </script>
</body>
</html>
