<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用管理功能演示</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        .demo-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .demo-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .demo-sidebar {
            background: #667eea;
            color: white;
            padding: 20px;
        }
        .demo-main {
            padding: 20px;
        }
        .demo-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .demo-nav li {
            margin-bottom: 10px;
        }
        .demo-nav a {
            color: white;
            text-decoration: none;
            padding: 10px;
            display: block;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .demo-nav a:hover,
        .demo-nav a.active {
            background: rgba(255, 255, 255, 0.2);
        }
        .step-indicator {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .feature-card h4 {
            color: #333;
            margin-bottom: 10px;
        }
        .feature-card p {
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🚀 应用管理模块演示</h1>
            <p>基于S1.md文档实现的完整应用管理功能展示</p>
        </div>

        <div class="demo-content">
            <div style="display: grid; grid-template-columns: 250px 1fr;">
                <div class="demo-sidebar">
                    <h3>📋 功能导航</h3>
                    <ul class="demo-nav">
                        <li><a href="#overview" class="active">功能概览</a></li>
                        <li><a href="#create" onclick="showCreateDemo()">创建应用</a></li>
                        <li><a href="#list" onclick="showListDemo()">应用列表</a></li>
                        <li><a href="#detail" onclick="showDetailDemo()">应用详情</a></li>
                        <li><a href="#manage" onclick="showManageDemo()">状态管理</a></li>
                    </ul>
                </div>

                <div class="demo-main">
                    <div id="overview" class="demo-section">
                        <h2>📱 应用管理功能概览</h2>
                        
                        <div class="step-indicator">
                            <strong>💡 提示：</strong>点击左侧导航可以体验不同的功能模块
                        </div>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <h4>🆕 创建应用</h4>
                                <p>支持创建新应用，自动生成app_key和secret_key，最多5个应用</p>
                                <button class="btn btn-primary" onclick="showCreateDemo()">体验创建</button>
                            </div>

                            <div class="feature-card">
                                <h4>📋 应用列表</h4>
                                <p>网格布局展示所有应用，显示基本信息和操作按钮</p>
                                <button class="btn btn-primary" onclick="showListDemo()">查看列表</button>
                            </div>

                            <div class="feature-card">
                                <h4>🔍 应用详情</h4>
                                <p>查看完整应用信息，管理密钥，支持显示/隐藏和复制</p>
                                <button class="btn btn-primary" onclick="showDetailDemo()">查看详情</button>
                            </div>

                            <div class="feature-card">
                                <h4>⚙️ 状态管理</h4>
                                <p>冻结/恢复应用，重置密钥，编辑应用名称</p>
                                <button class="btn btn-primary" onclick="showManageDemo()">管理应用</button>
                            </div>

                            <div class="feature-card">
                                <h4>🔐 密钥安全</h4>
                                <p>密钥默认隐藏，支持一键复制，重置时需要确认</p>
                                <button class="btn btn-primary" onclick="testCopyFunction()">测试复制</button>
                            </div>

                            <div class="feature-card">
                                <h4>📱 响应式设计</h4>
                                <p>适配桌面端和移动端，优雅的交互体验</p>
                                <button class="btn btn-primary" onclick="toggleMobileView()">切换视图</button>
                            </div>
                        </div>

                        <div class="step-indicator">
                            <strong>📋 业务规则：</strong>
                            <ul style="margin: 10px 0 0 20px;">
                                <li>每用户最多5个应用</li>
                                <li>app_key全局唯一（32位）</li>
                                <li>secret_key可重置（64位）</li>
                                <li>用户只能操作自己的应用</li>
                                <li>冻结应用无法调用API</li>
                            </ul>
                        </div>
                    </div>

                    <div id="demoContent" style="margin-top: 30px;">
                        <!-- 演示内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal()">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast"></div>

    <script src="api.js"></script>
    <script>
        // 模拟应用状态
        const appState = {
            currentUser: { id: 1, phone: '13800138000' },
            isLoggedIn: true
        };

        const applicationState = {
            applications: [
                {
                    id: 1,
                    name: '我的搜题应用',
                    type: 1,
                    app_key: 'abcd1234efgh5678ijkl9012mnop3456',
                    secret_key: 'abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz12',
                    status: 1,
                    created_at: '2024-01-01T12:00:00Z',
                    updated_at: '2024-01-01T12:00:00Z'
                },
                {
                    id: 2,
                    name: '测试应用',
                    type: 1,
                    app_key: 'efgh5678ijkl9012mnop3456abcd1234',
                    secret_key: 'zyxwvutsrqponmlkjihgfedcba0987654321zyxwvutsrqponmlkjihgfedcba',
                    status: 2,
                    created_at: '2024-01-02T10:30:00Z',
                    updated_at: '2024-01-02T10:30:00Z'
                }
            ],
            currentApplication: null,
            loading: false
        };

        // Toast管理器
        const toast = {
            success: (msg) => showToast(msg, 'success'),
            error: (msg) => showToast(msg, 'error'),
            warning: (msg) => showToast(msg, 'warning'),
            info: (msg) => showToast(msg, 'info')
        };

        function showToast(message, type) {
            const toastEl = document.getElementById('toast');
            toastEl.textContent = message;
            toastEl.className = `toast ${type} show`;
            setTimeout(() => {
                toastEl.classList.remove('show');
            }, 3000);
        }

        // 模态框管理
        function showModal() {
            document.getElementById('modal').style.display = 'block';
        }

        function hideModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 演示函数
        function showCreateDemo() {
            updateActiveNav('create');
            showCreateApplicationModal();
        }

        function showListDemo() {
            updateActiveNav('list');
            const content = document.getElementById('demoContent');
            content.innerHTML = `
                <h3>📋 应用列表演示</h3>
                <div class="apps-list">
                    ${applicationState.applications.map(app => createApplicationCard(app)).join('')}
                </div>
            `;
        }

        function showDetailDemo() {
            updateActiveNav('detail');
            showApplicationDetailModal(applicationState.applications[0]);
        }

        function showManageDemo() {
            updateActiveNav('manage');
            toast.info('状态管理功能演示 - 请在应用卡片中点击相关按钮');
            showListDemo();
        }

        function testCopyFunction() {
            copyKey('demo-key-12345', 'Demo Key');
        }

        function toggleMobileView() {
            const container = document.querySelector('.demo-container');
            container.style.maxWidth = container.style.maxWidth === '400px' ? '1200px' : '400px';
            toast.info('视图已切换');
        }

        function updateActiveNav(section) {
            document.querySelectorAll('.demo-nav a').forEach(a => a.classList.remove('active'));
            document.querySelector(`[href="#${section}"]`)?.classList.add('active');
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
    <script src="script.js"></script>
</body>
</html>
