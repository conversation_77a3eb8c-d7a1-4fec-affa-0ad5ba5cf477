<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码管理功能测试</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .api-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 15px;
        }
        .test-form {
            max-width: 400px;
        }
        .test-form .form-group {
            margin-bottom: 15px;
        }
        .test-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .test-form input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>密码管理功能测试</h1>
        <p>基于S1.md文档实现的密码管理API测试页面</p>
        
        <div class="test-section">
            <h3>1. 用户修改密码测试</h3>
            <div class="api-info">PUT /api/v1/user/{user_id}/change-password</div>
            <form class="test-form" onsubmit="testChangeUserPassword(event)">
                <div class="form-group">
                    <label>用户ID</label>
                    <input type="number" name="user_id" value="1" required>
                </div>
                <div class="form-group">
                    <label>原密码</label>
                    <input type="password" name="old_password" placeholder="请输入原密码" required>
                </div>
                <div class="form-group">
                    <label>新密码</label>
                    <input type="password" name="new_password" placeholder="6-20位，包含字母和数字" required>
                </div>
                <button type="submit" class="btn btn-primary">测试修改密码</button>
            </form>
            <div id="changePasswordResult" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 忘记密码 - 发送验证码测试</h3>
            <div class="api-info">POST /api/v1/user/forgot-password</div>
            <form class="test-form" onsubmit="testSendForgotCode(event)">
                <div class="form-group">
                    <label>手机号</label>
                    <input type="tel" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <button type="submit" class="btn btn-primary">测试发送验证码</button>
            </form>
            <div id="sendCodeResult" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 重置密码测试</h3>
            <div class="api-info">POST /api/v1/user/reset-password</div>
            <form class="test-form" onsubmit="testResetPassword(event)">
                <div class="form-group">
                    <label>手机号</label>
                    <input type="tel" name="phone" placeholder="请输入11位手机号" required>
                </div>
                <div class="form-group">
                    <label>验证码</label>
                    <input type="text" name="code" placeholder="请输入6位验证码" maxlength="6" required>
                </div>
                <div class="form-group">
                    <label>新密码</label>
                    <input type="password" name="new_password" placeholder="6-20位，包含字母和数字" required>
                </div>
                <button type="submit" class="btn btn-primary">测试重置密码</button>
            </form>
            <div id="resetPasswordResult" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 管理员登录测试</h3>
            <div class="api-info">POST /api/v1/admin/login</div>
            <form class="test-form" onsubmit="testAdminLogin(event)">
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" name="username" value="15688515913" required>
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" name="password" value="admin888" required>
                </div>
                <button type="submit" class="btn btn-primary">测试管理员登录</button>
            </form>
            <div id="adminLoginResult" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. 管理员重置用户密码测试</h3>
            <div class="api-info">PUT /api/v1/admin/{admin_id}/user/{user_id}/reset-password</div>
            <form class="test-form" onsubmit="testAdminResetUserPassword(event)">
                <div class="form-group">
                    <label>管理员ID</label>
                    <input type="number" name="admin_id" value="1" required>
                </div>
                <div class="form-group">
                    <label>用户ID</label>
                    <input type="number" name="user_id" value="1" required>
                </div>
                <div class="form-group">
                    <label>新密码</label>
                    <input type="password" name="new_password" placeholder="6-20位，包含字母和数字" required>
                </div>
                <button type="submit" class="btn btn-danger">测试重置用户密码</button>
            </form>
            <div id="adminResetResult" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>6. 密码强度验证测试</h3>
            <form class="test-form">
                <div class="form-group">
                    <label>测试密码</label>
                    <input type="password" id="testPassword" placeholder="输入密码查看强度" oninput="testPasswordStrength()">
                    <div class="password-strength" id="testPasswordStrength"></div>
                </div>
            </form>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast"></div>

    <script src="api.js"></script>
    <script>
        // 模拟Toast管理器
        const toast = {
            success: (msg) => showToast(msg, 'success'),
            error: (msg) => showToast(msg, 'error'),
            warning: (msg) => showToast(msg, 'warning'),
            info: (msg) => showToast(msg, 'info')
        };

        function showToast(message, type) {
            const toastEl = document.getElementById('toast');
            toastEl.textContent = message;
            toastEl.className = `toast ${type} show`;
            setTimeout(() => {
                toastEl.classList.remove('show');
            }, 3000);
        }

        // 显示测试结果
        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `test-result ${success ? 'success' : 'error'}`;
            element.innerHTML = `
                <strong>${success ? '✅ 成功' : '❌ 失败'}</strong><br>
                ${message}
                ${data ? `<br><pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }

        // 测试用户修改密码
        async function testChangeUserPassword(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            
            try {
                const result = await apiService.changeUserPassword(
                    formData.get('user_id'),
                    formData.get('old_password'),
                    formData.get('new_password')
                );
                
                showResult('changePasswordResult', result.success, 
                    result.success ? '密码修改成功' : result.error, result.data);
            } catch (error) {
                showResult('changePasswordResult', false, '网络错误: ' + error.message);
            }
        }

        // 测试发送验证码
        async function testSendForgotCode(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            
            try {
                const result = await apiService.sendForgotPasswordCode(formData.get('phone'));
                showResult('sendCodeResult', result.success, 
                    result.success ? '验证码发送成功' : result.error);
            } catch (error) {
                showResult('sendCodeResult', false, '网络错误: ' + error.message);
            }
        }

        // 测试重置密码
        async function testResetPassword(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            
            try {
                const result = await apiService.resetUserPassword(
                    formData.get('phone'),
                    formData.get('code'),
                    formData.get('new_password')
                );
                
                showResult('resetPasswordResult', result.success, 
                    result.success ? '密码重置成功' : result.error, result.data);
            } catch (error) {
                showResult('resetPasswordResult', false, '网络错误: ' + error.message);
            }
        }

        // 测试管理员登录
        async function testAdminLogin(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            
            try {
                const result = await apiService.adminLogin(
                    formData.get('username'),
                    formData.get('password')
                );
                
                showResult('adminLoginResult', result.success, 
                    result.success ? '管理员登录成功' : result.error, result.data);
            } catch (error) {
                showResult('adminLoginResult', false, '网络错误: ' + error.message);
            }
        }

        // 测试管理员重置用户密码
        async function testAdminResetUserPassword(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            
            try {
                const result = await apiService.adminResetUserPassword(
                    formData.get('admin_id'),
                    formData.get('user_id'),
                    formData.get('new_password')
                );
                
                showResult('adminResetResult', result.success, 
                    result.success ? '用户密码重置成功' : result.error, result.data);
            } catch (error) {
                showResult('adminResetResult', false, '网络错误: ' + error.message);
            }
        }

        // 测试密码强度
        function testPasswordStrength() {
            const input = document.getElementById('testPassword');
            const strengthElement = document.getElementById('testPasswordStrength');
            
            const password = input.value;
            if (!password) {
                strengthElement.textContent = '';
                strengthElement.className = 'password-strength';
                return;
            }

            const strength = ApiUtils.checkPasswordStrength(password);
            strengthElement.textContent = strength.message;
            strengthElement.className = `password-strength ${strength.strength}`;
        }
    </script>
</body>
</html>
